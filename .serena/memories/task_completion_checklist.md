# Task Completion Checklist for QuickCV

When completing a task in this project:

## Code Quality Checks
1. **DO NOT** run lint or format commands manually (per project instructions)
2. Ensure TypeScript types are properly defined
3. Follow existing patterns in the codebase
4. Use path alias `@/*` for imports

## Localization
1. Add any new text strings to both `/messages/en.json` and `/messages/ar.json`
2. Ensure RTL compatibility for Arabic content

## Database Changes
1. Generate migrations if schema changed: `bun run db:generate`
2. Run migrations: `bun run db:migrate`
3. Update seed data if needed

## Server Actions
1. Always use `requireAuth()` for authenticated endpoints
2. Verify user ownership on all operations
3. Follow existing server action patterns

## Testing Considerations
1. No test framework currently configured
2. Manual testing required for now

## Important Notes
- The project recently migrated from Rails backend to full-stack Next.js
- Using Clerk for authentication (migrated from custom auth)
- Database migrated from Prisma to Drizzle ORM with Turso
- Using UploadThing for file uploads instead of Active Storage

## References
- Always use Reactive-Resume project as reference: `/Users/<USER>/Downloads/Reactive-Resume-main`
- Use context7 for documentation lookups and external API references