# QuickCV Project Overview

QuickCV is a full-stack resume builder application.

## Tech Stack
- **Framework**: Next.js 15 with TypeScript and App Router
- **Database**: SQLite (development) with <PERSON><PERSON>zle ORM and Turso
- **Authentication**: Clerk for user management
- **UI Components**: HeroUI v2 with Tailwind CSS
- **Architecture**: Full-stack Next.js application using server actions
- **File Uploads**: UploadThing for photo uploads
- **Rich Text**: TipTap editor for descriptions
- **Package Manager**: Bun
- **Linting/Formatting**: Biome

## Project Purpose
A resume builder application that allows users to create professional resumes using various templates with ATS (Applicant Tracking System) scoring capabilities.

## Project Structure
- `/app` - Next.js App Router pages and API routes
- `/components` - Reusable UI components
- `/actions` - Server actions for backend logic
- `/lib` - Utilities and database client
- `/db` - Database schema and migrations
- `/config` - Schemas, color schemes, and configuration
- `/types` - TypeScript type definitions
- `/hooks` - Custom React hooks
- `/stores` - State management (Zustand)
- `/contexts` - React contexts