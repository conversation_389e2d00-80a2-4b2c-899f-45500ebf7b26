# Suggested Commands for QuickCV Development

## Development
- `bun run dev` - Start development server with Turbo
- `bun run dev:clean` - Clean dev environment and start fresh

## Building & Production
- `bun run build` - Build for production
- `bun run start` - Start production server

## Code Quality (DO NOT RUN MANUALLY - Per instructions)
- `bun run lint` - Run Biome linter with auto-fix
- `bun run lint:check` - Check linting without fixes
- `bun run format` - Format code with Biome
- `bun run format:check` - Check formatting without fixes

## Database
- `bun run db:generate` - Generate Drizzle migrations
- `bun run db:migrate` - Run migrations
- `bun run db:seed` - Seed database with templates
- `bun run db:studio` - Open Drizzle Studio for DB management
- `bun run db:local` - Start local Turso database

## Analysis
- `bun run knip` - Find unused dependencies and exports

## Clean Development
- `bun run clean` - Clean development environment
- `./scripts/clean-dev.sh` - Clean script

## System Commands (Darwin/macOS)
- `ls` - List files and directories
- `grep` - Search file contents (prefer ripgrep `rg` if available)
- `find` - Find files and directories
- `git` - Version control