# Code Style and Conventions for QuickCV

## TypeScript Configuration
- Strict mode enabled
- Target: ES2015
- Module: ESNext with bundler resolution
- JSX: Preserve
- Path alias: `@/*` maps to root directory

## Important Guidelines (from CLAUDE.md)
- **DO NOT** run format or lint fixes manually - produces unneeded git changes
- Always use icons, not SVG directly
- Always add translations for new text (ar and en locale JSON files)
- Use relevant AI agents for specific tasks

## File Organization
- Server actions in `/actions` directory
- Components in `/components` directory
- Database schema in `/db/schema.ts`
- Type definitions in `/types` directory
- Configuration in `/config` directory

## Authentication Pattern
- All server actions use `requireAuth()` from `lib/auth-clerk.ts`
- User ownership verification on all database operations

## Form Patterns
- Schema-driven forms defined in `config/schemas.ts`
- Generic form components with validation
- Auto-save functionality using React hooks

## Database Operations
- Use Drizzle ORM for all database operations
- Follow consistent patterns for nested resources
- Generic server actions handle CRUD operations

## Template System
- 12 resume templates with Pokemon-themed names
- Template registry in `components/resume/templates/template-registry.tsx`
- Each template accepts a `resume` prop with full resume data

## Localization
- Next-intl for multi-language support
- RTL layout support for Arabic
- Always add new keys to both ar and en locale files