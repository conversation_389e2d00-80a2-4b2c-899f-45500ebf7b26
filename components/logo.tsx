import * as React from "react";
import { IconSvgProps } from "@/types";

// Main QuickCV logo with document and lightning elements
export const QuickCVLogo: React.FC<IconSvgProps> = ({ size = 36, width, height, ...props }) => (
  <svg fill="none" height={size || height} viewBox="0 0 32 32" width={size || width} {...props}>
    {/* Document background */}
    <rect
      x="6"
      y="4"
      width="16"
      height="20"
      rx="2"
      ry="2"
      fill="currentColor"
      fillOpacity="0.1"
      stroke="currentColor"
      strokeWidth="1.5"
    />

    {/* Document header/title bar */}
    <rect x="8" y="7" width="8" height="1.5" rx="0.75" fill="currentColor" fillOpacity="0.8" />

    {/* Document lines representing text */}
    <rect x="8" y="10" width="10" height="1" rx="0.5" fill="currentColor" fillOpacity="0.6" />
    <rect x="8" y="12.5" width="12" height="1" rx="0.5" fill="currentColor" fillOpacity="0.6" />
    <rect x="8" y="15" width="9" height="1" rx="0.5" fill="currentColor" fillOpacity="0.6" />
    <rect x="8" y="17.5" width="11" height="1" rx="0.5" fill="currentColor" fillOpacity="0.6" />

    {/* Speed/Lightning element - representing "Quick" */}
    <path d="M24 8L20 14h3l-4 8 6-10h-3l2-4z" fill="currentColor" fillOpacity="0.9" />

    {/* Decorative corner fold */}
    <path d="M19 4v3h3L19 4z" fill="currentColor" fillOpacity="0.3" />
  </svg>
);

// Simplified version for small sizes (favicon, etc.)
export const QuickCVLogoMini: React.FC<IconSvgProps> = ({ size = 24, width, height, ...props }) => (
  <svg fill="none" height={size || height} viewBox="0 0 24 24" width={size || width} {...props}>
    {/* Simplified document */}
    <rect
      x="4"
      y="3"
      width="12"
      height="15"
      rx="1.5"
      fill="currentColor"
      fillOpacity="0.15"
      stroke="currentColor"
      strokeWidth="1.5"
    />

    {/* Document lines */}
    <rect x="6" y="6" width="6" height="1" rx="0.5" fill="currentColor" fillOpacity="0.7" />
    <rect x="6" y="8.5" width="8" height="1" rx="0.5" fill="currentColor" fillOpacity="0.7" />
    <rect x="6" y="11" width="7" height="1" rx="0.5" fill="currentColor" fillOpacity="0.7" />

    {/* Lightning bolt */}
    <path d="M18 5l-3 5h2l-3 6 4-7h-2l2-4z" fill="currentColor" />
  </svg>
);

// Icon-only version with just the lightning bolt
export const QuickCVIcon: React.FC<IconSvgProps> = ({ size = 24, width, height, ...props }) => (
  <svg fill="none" height={size || height} viewBox="0 0 24 24" width={size || width} {...props}>
    <path d="M13 2L4 14h6l-2 8 9-12h-6l2-8z" fill="currentColor" />
  </svg>
);

// Brand logo with text (for marketing materials)
export const QuickCVBrandLogo: React.FC<
  IconSvgProps & {
    showText?: boolean;
    textClassName?: string;
  }
> = ({ size = 40, width, height, showText = true, textClassName = "", ...props }) => (
  <div className="flex items-center gap-2">
    <QuickCVLogo size={size} width={width} height={height} {...props} />
    {showText && (
      <span
        className={`font-bold text-xl bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent ${textClassName}`}
      >
        QuickCV
      </span>
    )}
  </div>
);

// Export main logo as default
export default QuickCVLogo;
