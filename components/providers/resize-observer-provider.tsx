"use client";

import { useEffect } from "react";

export function ResizeObserverProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Apply the ResizeObserver fix on mount
    if (typeof window !== "undefined" && window.ResizeObserver) {
      const OriginalResizeObserver = window.ResizeObserver;
      
      class PatchedResizeObserver implements ResizeObserver {
        private originalObserver: ResizeObserver;
        
        constructor(callback: ResizeObserverCallback) {
          this.originalObserver = new OriginalResizeObserver(callback);
        }
        
        observe(target: Element, options?: ResizeObserverOptions): void {
          if (!target) {
            console.warn("ResizeObserver.observe called with null/undefined target");
            return;
          }
          
          if (!(target instanceof Element)) {
            console.warn("ResizeObserver.observe called with non-Element target:", target);
            return;
          }
          
          try {
            this.originalObserver.observe(target, options);
          } catch (error) {
            console.warn("ResizeObserver.observe error:", error);
          }
        }
        
        unobserve(target: Element): void {
          if (!target || !(target instanceof Element)) {
            return;
          }
          this.originalObserver.unobserve(target);
        }
        
        disconnect(): void {
          this.originalObserver.disconnect();
        }
      }
      
      // Override the global ResizeObserver
      (window as any).ResizeObserver = PatchedResizeObserver;
    }
  }, []);

  return <>{children}</>;
}