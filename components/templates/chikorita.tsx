import React from "react";
import SkillsDisplay from "@/components/resume/skills-display";
import { FullResume } from "@/db/schema";

interface ChikoritaTemplateProps {
  resume: FullResume;
}

export default function ChikoritaTemplate({ resume }: ChikoritaTemplateProps) {
  const colorClasses = {
    blue: {
      primary: "bg-blue-600 text-white",
      secondary: "bg-blue-100 text-blue-800",
      accent: "text-blue-600 border-blue-600",
      light: "bg-blue-50",
    },
    green: {
      primary: "bg-green-600 text-white",
      secondary: "bg-green-100 text-green-800",
      accent: "text-green-600 border-green-600",
      light: "bg-green-50",
    },
    purple: {
      primary: "bg-purple-600 text-white",
      secondary: "bg-purple-100 text-purple-800",
      accent: "text-purple-600 border-purple-600",
      light: "bg-purple-50",
    },
    red: {
      primary: "bg-red-600 text-white",
      secondary: "bg-red-100 text-red-800",
      accent: "text-red-600 border-red-600",
      light: "bg-red-50",
    },
    orange: {
      primary: "bg-orange-600 text-white",
      secondary: "bg-orange-100 text-orange-800",
      accent: "text-orange-600 border-orange-600",
      light: "bg-orange-50",
    },
    teal: {
      primary: "bg-teal-600 text-white",
      secondary: "bg-teal-100 text-teal-800",
      accent: "text-teal-600 border-teal-600",
      light: "bg-teal-50",
    },
    pink: {
      primary: "bg-pink-600 text-white",
      secondary: "bg-pink-100 text-pink-800",
      accent: "text-pink-600 border-pink-600",
      light: "bg-pink-50",
    },
    indigo: {
      primary: "bg-indigo-600 text-white",
      secondary: "bg-indigo-100 text-indigo-800",
      accent: "text-indigo-600 border-indigo-600",
      light: "bg-indigo-50",
    },
  };

  const selectedColor = colorClasses[resume.colorScheme as keyof typeof colorClasses] || colorClasses.blue;

  return (
    <div
      className={`max-w-4xl mx-auto bg-white shadow-lg font-${resume.fontFamily || "inter"} text-gray-900 print:shadow-none overflow-hidden`}
    >
      {/* Header Section - Colorful banner */}
      <header className={`${selectedColor.primary} px-8 py-10 relative overflow-hidden`}>
        <div className="absolute inset-0 bg-black bg-opacity-10"></div>
        <div className="relative z-10 flex items-center justify-between">
          <div className="flex-1">
            {/* Name - Most prominent with white text */}
            <h1 className="text-5xl font-bold text-white mb-3 leading-tight tracking-wide">
              {resume.firstName} {resume.lastName}
            </h1>

            {/* Job Title - Secondary prominence with color contrast */}
            <h2 className="text-2xl font-semibold text-white opacity-90 mb-4 leading-relaxed">{resume.jobTitle}</h2>

            {/* Contact Information - Horizontal layout in header */}
            <div className="flex flex-wrap gap-6 text-white opacity-85 text-base font-medium">
              {resume.email && (
                <div className="flex items-center">
                  <span className="mr-2">✉</span>
                  {resume.email}
                </div>
              )}
              {resume.phone && (
                <div className="flex items-center">
                  <span className="mr-2">📞</span>
                  {resume.phone}
                </div>
              )}
              {resume.website && (
                <div className="flex items-center">
                  <span className="mr-2">🌐</span>
                  {resume.website}
                </div>
              )}
              {(resume.city || resume.country) && (
                <div className="flex items-center">
                  <span className="mr-2">📍</span>
                  {[resume.city, resume.country].filter(Boolean).join(", ")}
                </div>
              )}
            </div>
          </div>

          {/* Photo - Circular with white border */}
          {resume.showPhoto && resume.photo && (
            <div className="ml-8 flex-shrink-0">
              <img
                src={resume.photo}
                alt={`${resume.firstName} ${resume.lastName}`}
                className="w-36 h-36 rounded-full object-cover border-4 border-white shadow-xl"
              />
            </div>
          )}
        </div>

        {/* Bio in colored section */}
        {resume.bio && (
          <div className="relative z-10 mt-6 max-w-4xl">
            <p className="text-lg text-white opacity-90 leading-relaxed">{resume.bio}</p>
          </div>
        )}
      </header>

      {/* Two-column layout with colorful sections */}
      <div className="flex">
        {/* Left Column - Main content */}
        <div className="w-2/3 px-8 py-8">
          {/* Experience Section */}
          {resume.experiences && resume.experiences.length > 0 && (
            <section className="mb-10">
              <div className={`${selectedColor.secondary} px-6 py-4 rounded-lg mb-6`}>
                <h3 className="text-2xl font-bold uppercase tracking-wide flex items-center">
                  <span className="mr-3">💼</span>
                  Professional Experience
                </h3>
              </div>
              <div className="space-y-8">
                {resume.experiences.map((experience) => (
                  <div
                    key={experience.id}
                    className={`border-l-4 ${selectedColor.accent.split(" ")[1]} pl-6 relative ${selectedColor.light} p-6 rounded-r-lg`}
                  >
                    <div
                      className={`absolute -left-3 top-8 w-6 h-6 ${selectedColor.primary.split(" ")[0]} rounded-full border-4 border-white`}
                    ></div>

                    {/* Job Title - Prominent */}
                    <h4 className="text-xl font-bold text-gray-900 mb-2 leading-tight">{experience.title}</h4>

                    {/* Company and Date */}
                    <div className="flex items-center justify-between mb-3">
                      <h5 className={`text-lg font-semibold ${selectedColor.accent.split(" ")[0]} leading-relaxed`}>
                        {experience.company}
                      </h5>
                      <span className="text-base font-semibold text-gray-700 bg-white px-3 py-1 rounded-full">
                        {experience.startDate} - {experience.isCurrent ? "Present" : experience.endDate}
                      </span>
                    </div>

                    {/* Location */}
                    {(experience.city || experience.country) && (
                      <p className="text-sm text-gray-600 mb-4 font-medium">
                        📍 {[experience.city, experience.country].filter(Boolean).join(", ")}
                      </p>
                    )}

                    {/* Description */}
                    {experience.description && (
                      <div
                        className="text-base text-gray-700 leading-relaxed prose prose-base max-w-none"
                        dangerouslySetInnerHTML={{ __html: experience.description }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Projects Section */}
          {resume.projects && resume.projects.length > 0 && (
            <section className="mb-10">
              <div className={`${selectedColor.secondary} px-6 py-4 rounded-lg mb-6`}>
                <h3 className="text-2xl font-bold uppercase tracking-wide flex items-center">
                  <span className="mr-3">🚀</span>
                  Key Projects
                </h3>
              </div>
              <div className="space-y-6">
                {resume.projects.map((project) => (
                  <div
                    key={project.id}
                    className={`border-l-4 ${selectedColor.accent.split(" ")[1]} pl-6 relative ${selectedColor.light} p-6 rounded-r-lg`}
                  >
                    <div
                      className={`absolute -left-3 top-6 w-6 h-6 ${selectedColor.primary.split(" ")[0]} rounded-full border-4 border-white`}
                    ></div>

                    {/* Project Title */}
                    <h4 className="text-lg font-bold text-gray-900 mb-2 leading-tight">{project.title}</h4>

                    {/* Client and Date */}
                    <div className="flex items-center justify-between mb-3">
                      {project.client && (
                        <h5 className={`text-base font-semibold ${selectedColor.accent.split(" ")[0]} leading-relaxed`}>
                          {project.client}
                        </h5>
                      )}
                      <span className="text-sm font-semibold text-gray-700 bg-white px-3 py-1 rounded-full">
                        {project.startDate} - {project.endDate}
                      </span>
                    </div>

                    {/* URL */}
                    {project.url && (
                      <p className="text-sm text-gray-600 mb-3">
                        <a
                          href={project.url}
                          className={`${selectedColor.accent.split(" ")[0]} hover:underline font-medium`}
                        >
                          🌐 {project.url}
                        </a>
                      </p>
                    )}

                    {/* Description */}
                    {project.description && (
                      <div
                        className="text-sm text-gray-700 leading-relaxed prose prose-sm max-w-none"
                        dangerouslySetInnerHTML={{ __html: project.description }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>

        {/* Right Column - Sidebar with colorful sections */}
        <div className="w-1/3 bg-gray-50 px-6 py-8 border-l border-gray-200">
          {/* Education Section */}
          {resume.educations && resume.educations.length > 0 && (
            <section className="mb-8">
              <div className={`${selectedColor.secondary} px-4 py-3 rounded-lg mb-4`}>
                <h3 className="text-lg font-bold uppercase tracking-wide flex items-center">
                  <span className="mr-2">🎓</span>
                  Education
                </h3>
              </div>
              <div className="space-y-4">
                {resume.educations.map((education) => (
                  <div
                    key={education.id}
                    className={`${selectedColor.light} p-4 rounded-lg border-l-4 ${selectedColor.accent.split(" ")[1]}`}
                  >
                    {/* Degree - Prominent */}
                    <h4 className="text-base font-bold text-gray-900 mb-1 leading-tight">{education.degree}</h4>

                    {/* Field of Study */}
                    {education.fieldOfStudy && (
                      <p className="text-sm font-semibold text-gray-700 mb-2">{education.fieldOfStudy}</p>
                    )}

                    {/* Institution */}
                    <h5 className={`text-sm font-semibold ${selectedColor.accent.split(" ")[0]} mb-2`}>
                      {education.institution}
                    </h5>

                    {/* Date and Location */}
                    <div className="text-xs text-gray-600 space-y-1">
                      <p className="font-medium">
                        {education.startDate} - {education.isCurrent ? "Present" : education.endDate}
                      </p>
                      {(education.city || education.country) && (
                        <p>📍 {[education.city, education.country].filter(Boolean).join(", ")}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Skills Section */}
          {resume.skills && resume.skills.length > 0 && (
            <section className="mb-8">
              <div className={`${selectedColor.secondary} px-4 py-3 rounded-lg mb-4`}>
                <h3 className="text-lg font-bold uppercase tracking-wide flex items-center">
                  <span className="mr-2">⚙️</span>
                  Skills
                </h3>
              </div>
              <SkillsDisplay
                skills={resume.skills}
                colorScheme={resume.colorScheme}
                variant="progress-bars"
                groupByCategory={true}
                maxColumns={1}
                showProficiency={true}
              />
            </section>
          )}

          {/* Languages Section */}
          {resume.languages && resume.languages.length > 0 && (
            <section className="mb-8">
              <div className={`${selectedColor.secondary} px-4 py-3 rounded-lg mb-4`}>
                <h3 className="text-lg font-bold uppercase tracking-wide flex items-center">
                  <span className="mr-2">🌍</span>
                  Languages
                </h3>
              </div>
              <div className="space-y-4">
                {resume.languages.map((language) => (
                  <div key={language.id} className={`${selectedColor.light} p-3 rounded-lg`}>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-semibold text-gray-800">{language.name}</span>
                      <span className="text-xs text-gray-600 font-medium bg-white px-2 py-1 rounded-full">
                        {language.proficiency}%
                      </span>
                    </div>
                    <div className="w-full bg-white rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${selectedColor.primary.split(" ")[0]}`}
                        style={{ width: `${language.proficiency}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Certifications Section */}
          {resume.certifications && resume.certifications.length > 0 && (
            <section className="mb-8">
              <div className={`${selectedColor.secondary} px-4 py-3 rounded-lg mb-4`}>
                <h3 className="text-lg font-bold uppercase tracking-wide flex items-center">
                  <span className="mr-2">🏆</span>
                  Certifications
                </h3>
              </div>
              <div className="space-y-3">
                {resume.certifications.map((cert) => (
                  <div key={cert.id} className={`${selectedColor.light} p-3 rounded-lg`}>
                    <h4 className="text-sm font-bold text-gray-900 leading-tight mb-1">{cert.title}</h4>
                    <p className="text-xs text-gray-600 font-medium">{cert.issuer}</p>
                    {cert.dateReceived && <p className="text-xs text-gray-500 mt-1">{cert.dateReceived}</p>}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Awards Section */}
          {resume.awards && resume.awards.length > 0 && (
            <section className="mb-8">
              <div className={`${selectedColor.secondary} px-4 py-3 rounded-lg mb-4`}>
                <h3 className="text-lg font-bold uppercase tracking-wide flex items-center">
                  <span className="mr-2">🌟</span>
                  Awards
                </h3>
              </div>
              <div className="space-y-3">
                {resume.awards.map((award) => (
                  <div key={award.id} className={`${selectedColor.light} p-3 rounded-lg`}>
                    <h4 className="text-sm font-bold text-gray-900 leading-tight mb-1">{award.title}</h4>
                    <p className="text-xs text-gray-600 font-medium">{award.issuer}</p>
                    {award.dateReceived && <p className="text-xs text-gray-500 mt-1">{award.dateReceived}</p>}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
}
