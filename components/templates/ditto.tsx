import React from "react";
import SkillsDisplay from "@/components/resume/skills-display";
import { FullResume } from "@/db/schema";

interface DittoTemplateProps {
  resume: FullResume;
}

export default function DittoTemplate({ resume }: DittoTemplateProps) {
  const colorClasses = {
    blue: "text-blue-600",
    green: "text-green-600",
    purple: "text-purple-600",
    red: "text-red-600",
    orange: "text-orange-600",
    teal: "text-teal-600",
    pink: "text-pink-600",
    indigo: "text-indigo-600",
  };

  const selectedColor = colorClasses[resume.colorScheme as keyof typeof colorClasses] || colorClasses.blue;

  return (
    <div className={`max-w-4xl mx-auto bg-white font-${resume.fontFamily || "inter"} text-gray-900 print:shadow-none`}>
      {/* Minimalist Header */}
      <header className="px-8 py-12 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            {/* Name - Clean and prominent */}
            <h1 className="text-6xl font-light text-gray-900 mb-4 leading-none tracking-tight">{resume.firstName}</h1>
            <h1 className="text-6xl font-bold text-gray-900 mb-6 leading-none tracking-tight">{resume.lastName}</h1>

            {/* Job Title - Subtle accent */}
            <h2 className={`text-2xl font-normal ${selectedColor} mb-8 leading-relaxed tracking-wide`}>
              {resume.jobTitle}
            </h2>
          </div>

          {/* Photo - Clean circular */}
          {resume.showPhoto && resume.photo && (
            <div className="ml-12 flex-shrink-0">
              <img
                src={resume.photo}
                alt={`${resume.firstName} ${resume.lastName}`}
                className="w-40 h-40 rounded-full object-cover grayscale hover:grayscale-0 transition-all duration-300"
              />
            </div>
          )}
        </div>

        {/* Bio - Clean typography */}
        {resume.bio && (
          <div className="max-w-4xl mt-8">
            <p className="text-lg text-gray-600 leading-relaxed font-light">{resume.bio}</p>
          </div>
        )}

        {/* Contact - Minimal horizontal layout */}
        <div className="flex flex-wrap gap-8 mt-8 text-sm text-gray-600">
          {resume.email && <span className="font-medium">{resume.email}</span>}
          {resume.phone && <span className="font-medium">{resume.phone}</span>}
          {resume.website && <span className="font-medium">{resume.website}</span>}
          {(resume.city || resume.country) && (
            <span className="font-medium">{[resume.city, resume.country].filter(Boolean).join(", ")}</span>
          )}
        </div>
      </header>

      {/* Content - Minimal sections */}
      <div className="px-8 py-8">
        {/* Experience Section */}
        {resume.experiences && resume.experiences.length > 0 && (
          <section className="mb-16">
            <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-widest mb-8 border-b border-gray-100 pb-4">
              Experience
            </h3>
            <div className="space-y-12">
              {resume.experiences.map((experience) => (
                <div key={experience.id}>
                  {/* Job Title - Most prominent */}
                  <h4 className="text-2xl font-bold text-gray-900 mb-2 leading-tight">{experience.title}</h4>

                  {/* Company and Date - Clean layout */}
                  <div className="flex items-center justify-between mb-3">
                    <h5 className={`text-lg font-medium ${selectedColor} leading-relaxed`}>{experience.company}</h5>
                    <span className="text-base text-gray-500 font-light">
                      {experience.startDate} — {experience.isCurrent ? "Present" : experience.endDate}
                    </span>
                  </div>

                  {/* Location - Subtle */}
                  {(experience.city || experience.country) && (
                    <p className="text-sm text-gray-400 mb-4 font-light">
                      {[experience.city, experience.country].filter(Boolean).join(", ")}
                    </p>
                  )}

                  {/* Description - Clean readable text */}
                  {experience.description && (
                    <div
                      className="text-base text-gray-700 leading-relaxed font-light prose prose-base max-w-none prose-headings:font-medium prose-p:text-gray-700"
                      dangerouslySetInnerHTML={{ __html: experience.description }}
                    />
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Education Section */}
        {resume.educations && resume.educations.length > 0 && (
          <section className="mb-16">
            <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-widest mb-8 border-b border-gray-100 pb-4">
              Education
            </h3>
            <div className="space-y-8">
              {resume.educations.map((education) => (
                <div key={education.id}>
                  {/* Degree - Prominent */}
                  <h4 className="text-xl font-bold text-gray-900 mb-2 leading-tight">{education.degree}</h4>

                  {/* Field of Study */}
                  {education.fieldOfStudy && (
                    <p className="text-lg text-gray-700 mb-1 font-light">{education.fieldOfStudy}</p>
                  )}

                  {/* Institution and Date */}
                  <div className="flex items-center justify-between mb-2">
                    <h5 className={`text-base font-medium ${selectedColor} leading-relaxed`}>
                      {education.institution}
                    </h5>
                    <span className="text-sm text-gray-500 font-light">
                      {education.startDate} — {education.isCurrent ? "Present" : education.endDate}
                    </span>
                  </div>

                  {/* Location */}
                  {(education.city || education.country) && (
                    <p className="text-sm text-gray-400 mb-3 font-light">
                      {[education.city, education.country].filter(Boolean).join(", ")}
                    </p>
                  )}

                  {/* Description */}
                  {education.description && (
                    <div
                      className="text-sm text-gray-600 leading-relaxed font-light"
                      dangerouslySetInnerHTML={{ __html: education.description }}
                    />
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Projects Section */}
        {resume.projects && resume.projects.length > 0 && (
          <section className="mb-16">
            <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-widest mb-8 border-b border-gray-100 pb-4">
              Projects
            </h3>
            <div className="space-y-8">
              {resume.projects.map((project) => (
                <div key={project.id}>
                  {/* Project Title */}
                  <h4 className="text-xl font-bold text-gray-900 mb-2 leading-tight">{project.title}</h4>

                  {/* Client and Date */}
                  <div className="flex items-center justify-between mb-2">
                    {project.client && (
                      <h5 className={`text-base font-medium ${selectedColor} leading-relaxed`}>{project.client}</h5>
                    )}
                    <span className="text-sm text-gray-500 font-light">
                      {project.startDate} — {project.endDate}
                    </span>
                  </div>

                  {/* URL */}
                  {project.url && (
                    <p className="text-sm text-gray-400 mb-3 font-light">
                      <a href={project.url} className={`${selectedColor} hover:underline`}>
                        {project.url}
                      </a>
                    </p>
                  )}

                  {/* Description */}
                  {project.description && (
                    <div
                      className="text-base text-gray-700 leading-relaxed font-light prose prose-base max-w-none prose-headings:font-medium prose-p:text-gray-700"
                      dangerouslySetInnerHTML={{ __html: project.description }}
                    />
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Skills Section - Minimal grid */}
        {resume.skills && resume.skills.length > 0 && (
          <section className="mb-16">
            <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-widest mb-8 border-b border-gray-100 pb-4">
              Skills
            </h3>
            <SkillsDisplay
              skills={resume.skills}
              colorScheme={resume.colorScheme}
              variant="minimal"
              groupByCategory={true}
              maxColumns={3}
              showProficiency={true}
            />
          </section>
        )}

        {/* Additional sections in minimal grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
          {/* Languages */}
          {resume.languages && resume.languages.length > 0 && (
            <section>
              <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-widest mb-6 border-b border-gray-100 pb-3">
                Languages
              </h3>
              <div className="space-y-4">
                {resume.languages.map((language) => (
                  <div key={language.id}>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-gray-800">{language.name}</span>
                      <span className="text-xs text-gray-400 font-light">{language.proficiency}%</span>
                    </div>
                    <div className="w-full bg-gray-100 h-px">
                      <div
                        className={`h-px ${selectedColor.replace("text-", "bg-")}`}
                        style={{ width: `${language.proficiency}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Certifications */}
          {resume.certifications && resume.certifications.length > 0 && (
            <section>
              <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-widest mb-6 border-b border-gray-100 pb-3">
                Certifications
              </h3>
              <div className="space-y-4">
                {resume.certifications.map((cert) => (
                  <div key={cert.id}>
                    <h4 className="text-sm font-medium text-gray-900 leading-tight mb-1">{cert.title}</h4>
                    <p className="text-xs text-gray-500 font-light">{cert.issuer}</p>
                    {cert.dateReceived && <p className="text-xs text-gray-400 font-light">{cert.dateReceived}</p>}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Awards */}
          {resume.awards && resume.awards.length > 0 && (
            <section>
              <h3 className="text-sm font-semibold text-gray-400 uppercase tracking-widest mb-6 border-b border-gray-100 pb-3">
                Awards
              </h3>
              <div className="space-y-4">
                {resume.awards.map((award) => (
                  <div key={award.id}>
                    <h4 className="text-sm font-medium text-gray-900 leading-tight mb-1">{award.title}</h4>
                    <p className="text-xs text-gray-500 font-light">{award.issuer}</p>
                    {award.dateReceived && <p className="text-xs text-gray-400 font-light">{award.dateReceived}</p>}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
}
