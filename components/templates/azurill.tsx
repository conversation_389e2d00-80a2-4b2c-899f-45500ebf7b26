import React from "react";
import SkillsDisplay from "@/components/resume/skills-display";
import { FullResume } from "@/db/schema";

interface AzurillTemplateProps {
  resume: FullResume;
}

export default function AzurillTemplate({ resume }: AzurillTemplateProps) {
  const colorClasses = {
    blue: "text-blue-600 border-blue-600",
    green: "text-green-600 border-green-600",
    purple: "text-purple-600 border-purple-600",
    red: "text-red-600 border-red-600",
    orange: "text-orange-600 border-orange-600",
    teal: "text-teal-600 border-teal-600",
    pink: "text-pink-600 border-pink-600",
    indigo: "text-indigo-600 border-indigo-600",
  };

  const selectedColor = colorClasses[resume.colorScheme as keyof typeof colorClasses] || colorClasses.blue;

  return (
    <div
      className={`max-w-4xl mx-auto bg-white shadow-sm font-${resume.fontFamily || "inter"} text-gray-900 print:shadow-none`}
    >
      {/* Header Section */}
      <header className="bg-gray-50 px-8 py-10 border-b border-gray-200">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            {/* Name - Most prominent */}
            <h1 className="text-4xl font-bold text-gray-900 mb-2 leading-tight tracking-tight">
              {resume.firstName} {resume.lastName}
            </h1>

            {/* Job Title - Secondary prominence */}
            <h2 className={`text-xl font-semibold ${selectedColor.split(" ")[0]} mb-4 leading-relaxed`}>
              {resume.jobTitle}
            </h2>

            {/* Bio - Readable body text */}
            {resume.bio && <p className="text-base text-gray-700 leading-relaxed max-w-3xl mb-6">{resume.bio}</p>}
          </div>

          {/* Photo */}
          {resume.showPhoto && resume.photo && (
            <div className="ml-8 flex-shrink-0">
              <img
                src={resume.photo}
                alt={`${resume.firstName} ${resume.lastName}`}
                className="w-32 h-32 rounded-full object-cover border-4 border-white shadow-md"
              />
            </div>
          )}
        </div>

        {/* Contact Information - Well-formatted grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 text-sm">
          {resume.email && (
            <div className="flex items-center">
              <span className="font-medium text-gray-600">Email:</span>
              <span className="ml-2 text-gray-800">{resume.email}</span>
            </div>
          )}
          {resume.phone && (
            <div className="flex items-center">
              <span className="font-medium text-gray-600">Phone:</span>
              <span className="ml-2 text-gray-800">{resume.phone}</span>
            </div>
          )}
          {resume.website && (
            <div className="flex items-center">
              <span className="font-medium text-gray-600">Website:</span>
              <span className="ml-2 text-gray-800">{resume.website}</span>
            </div>
          )}
          {(resume.city || resume.country) && (
            <div className="flex items-center">
              <span className="font-medium text-gray-600">Location:</span>
              <span className="ml-2 text-gray-800">{[resume.city, resume.country].filter(Boolean).join(", ")}</span>
            </div>
          )}
        </div>
      </header>

      {/* Two-column layout */}
      <div className="flex">
        {/* Left Column */}
        <div className="w-2/3 px-8 py-8">
          {/* Experience Section */}
          {resume.experiences && resume.experiences.length > 0 && (
            <section className="mb-8">
              <h3
                className={`text-2xl font-bold ${selectedColor.split(" ")[0]} mb-6 pb-2 border-b-2 ${selectedColor.split(" ")[1]} uppercase tracking-wide`}
              >
                Experience
              </h3>
              <div className="space-y-6">
                {resume.experiences.map((experience) => (
                  <div key={experience.id} className="relative pl-6">
                    <div
                      className={`absolute left-0 top-2 w-3 h-3 ${selectedColor.split(" ")[0].replace("text-", "bg-")} rounded-full`}
                    ></div>

                    {/* Job Title - Prominent */}
                    <h4 className="text-lg font-semibold text-gray-900 mb-1 leading-tight">{experience.title}</h4>

                    {/* Company - Clear but secondary */}
                    <div className="flex items-center justify-between mb-2">
                      <h5 className={`text-base font-medium ${selectedColor.split(" ")[0]} leading-relaxed`}>
                        {experience.company}
                      </h5>
                      <span className="text-sm text-gray-600 font-medium">
                        {experience.startDate} - {experience.isCurrent ? "Present" : experience.endDate}
                      </span>
                    </div>

                    {/* Location */}
                    {(experience.city || experience.country) && (
                      <p className="text-sm text-gray-600 mb-3">
                        {[experience.city, experience.country].filter(Boolean).join(", ")}
                      </p>
                    )}

                    {/* Description - Readable with good line height */}
                    {experience.description && (
                      <div
                        className="text-sm text-gray-700 leading-relaxed prose prose-sm max-w-none"
                        dangerouslySetInnerHTML={{ __html: experience.description }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Projects Section */}
          {resume.projects && resume.projects.length > 0 && (
            <section className="mb-8">
              <h3
                className={`text-2xl font-bold ${selectedColor.split(" ")[0]} mb-6 pb-2 border-b-2 ${selectedColor.split(" ")[1]} uppercase tracking-wide`}
              >
                Projects
              </h3>
              <div className="space-y-6">
                {resume.projects.map((project) => (
                  <div key={project.id} className="relative pl-6">
                    <div
                      className={`absolute left-0 top-2 w-3 h-3 ${selectedColor.split(" ")[0].replace("text-", "bg-")} rounded-full`}
                    ></div>

                    {/* Project Title */}
                    <h4 className="text-lg font-semibold text-gray-900 mb-1 leading-tight">{project.title}</h4>

                    {/* Client and Date */}
                    <div className="flex items-center justify-between mb-2">
                      {project.client && (
                        <h5 className={`text-base font-medium ${selectedColor.split(" ")[0]} leading-relaxed`}>
                          {project.client}
                        </h5>
                      )}
                      <span className="text-sm text-gray-600 font-medium">
                        {project.startDate} - {project.endDate}
                      </span>
                    </div>

                    {/* URL */}
                    {project.url && (
                      <p className="text-sm text-gray-600 mb-3">
                        <a href={project.url} className={`${selectedColor.split(" ")[0]} hover:underline`}>
                          {project.url}
                        </a>
                      </p>
                    )}

                    {/* Description */}
                    {project.description && (
                      <div
                        className="text-sm text-gray-700 leading-relaxed prose prose-sm max-w-none"
                        dangerouslySetInnerHTML={{ __html: project.description }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>

        {/* Right Column */}
        <div className="w-1/3 bg-gray-50 px-6 py-8 border-l border-gray-200">
          {/* Education Section */}
          {resume.educations && resume.educations.length > 0 && (
            <section className="mb-8">
              <h3 className={`text-xl font-bold ${selectedColor.split(" ")[0]} mb-4 uppercase tracking-wide`}>
                Education
              </h3>
              <div className="space-y-4">
                {resume.educations.map((education) => (
                  <div key={education.id}>
                    {/* Degree - Prominent */}
                    <h4 className="text-base font-semibold text-gray-900 mb-1 leading-tight">{education.degree}</h4>

                    {/* Field of Study */}
                    {education.fieldOfStudy && (
                      <p className="text-sm font-medium text-gray-700 mb-1">{education.fieldOfStudy}</p>
                    )}

                    {/* Institution */}
                    <h5 className={`text-sm font-medium ${selectedColor.split(" ")[0]} mb-1`}>
                      {education.institution}
                    </h5>

                    {/* Date and Location */}
                    <div className="text-xs text-gray-600">
                      <p>
                        {education.startDate} - {education.isCurrent ? "Present" : education.endDate}
                      </p>
                      {(education.city || education.country) && (
                        <p>{[education.city, education.country].filter(Boolean).join(", ")}</p>
                      )}
                    </div>

                    {/* Description */}
                    {education.description && (
                      <div
                        className="text-xs text-gray-700 leading-relaxed mt-2"
                        dangerouslySetInnerHTML={{ __html: education.description }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Skills Section */}
          {resume.skills && resume.skills.length > 0 && (
            <section className="mb-8">
              <h3 className={`text-xl font-bold ${selectedColor.split(" ")[0]} mb-4 uppercase tracking-wide`}>
                Skills
              </h3>
              <SkillsDisplay
                skills={resume.skills}
                colorScheme={resume.colorScheme}
                variant="progress-bars"
                groupByCategory={true}
                maxColumns={1}
                showProficiency={true}
              />
            </section>
          )}

          {/* Languages Section */}
          {resume.languages && resume.languages.length > 0 && (
            <section className="mb-8">
              <h3 className={`text-xl font-bold ${selectedColor.split(" ")[0]} mb-4 uppercase tracking-wide`}>
                Languages
              </h3>
              <div className="space-y-3">
                {resume.languages.map((language) => (
                  <div key={language.id}>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-gray-800">{language.name}</span>
                      <span className="text-xs text-gray-600">{language.proficiency}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${selectedColor.split(" ")[0].replace("text-", "bg-")}`}
                        style={{ width: `${language.proficiency}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Certifications Section */}
          {resume.certifications && resume.certifications.length > 0 && (
            <section className="mb-8">
              <h3 className={`text-xl font-bold ${selectedColor.split(" ")[0]} mb-4 uppercase tracking-wide`}>
                Certifications
              </h3>
              <div className="space-y-3">
                {resume.certifications.map((cert) => (
                  <div key={cert.id}>
                    <h4 className="text-sm font-semibold text-gray-900 leading-tight">{cert.title}</h4>
                    <p className="text-xs text-gray-600 mb-1">{cert.issuer}</p>
                    {cert.dateReceived && <p className="text-xs text-gray-500">{cert.dateReceived}</p>}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Awards Section */}
          {resume.awards && resume.awards.length > 0 && (
            <section className="mb-8">
              <h3 className={`text-xl font-bold ${selectedColor.split(" ")[0]} mb-4 uppercase tracking-wide`}>
                Awards
              </h3>
              <div className="space-y-3">
                {resume.awards.map((award) => (
                  <div key={award.id}>
                    <h4 className="text-sm font-semibold text-gray-900 leading-tight">{award.title}</h4>
                    <p className="text-xs text-gray-600 mb-1">{award.issuer}</p>
                    {award.dateReceived && <p className="text-xs text-gray-500">{award.dateReceived}</p>}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
}
