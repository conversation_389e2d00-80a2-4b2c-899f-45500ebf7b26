import React from "react";
import { FullResume } from "@/db/schema";

interface NosepassTemplateProps {
  resume: FullResume;
}

export default function NosepassTemplate({ resume }: NosepassTemplateProps) {
  const colorClasses = {
    blue: "text-blue-800 border-blue-800 bg-blue-800",
    green: "text-green-800 border-green-800 bg-green-800",
    purple: "text-purple-800 border-purple-800 bg-purple-800",
    red: "text-red-800 border-red-800 bg-red-800",
    orange: "text-orange-800 border-orange-800 bg-orange-800",
    teal: "text-teal-800 border-teal-800 bg-teal-800",
    pink: "text-pink-800 border-pink-800 bg-pink-800",
    indigo: "text-indigo-800 border-indigo-800 bg-indigo-800",
  };

  const selectedColor = colorClasses[resume.colorScheme as keyof typeof colorClasses] || colorClasses.blue;

  return (
    <div
      className={`max-w-4xl mx-auto bg-white shadow-sm font-${resume.fontFamily || "inter"} text-gray-900 print:shadow-none border border-gray-300`}
    >
      {/* Classic header with formal styling */}
      <header className="bg-gray-800 text-white px-8 py-8">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            {/* Name - Traditional formal style */}
            <h1 className="text-4xl font-bold text-white mb-2 leading-tight tracking-wide">
              {resume.firstName} {resume.lastName}
            </h1>

            {/* Job Title - Professional accent */}
            <h2 className="text-xl font-semibold text-gray-300 mb-4 leading-relaxed uppercase tracking-widest">
              {resume.jobTitle}
            </h2>

            {/* Professional Summary */}
            {resume.bio && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-400 mb-3 uppercase tracking-wide border-b border-gray-600 pb-2">
                  Professional Summary
                </h3>
                <p className="text-base text-gray-200 leading-relaxed max-w-3xl">{resume.bio}</p>
              </div>
            )}
          </div>

          {/* Photo - Traditional styling */}
          {resume.showPhoto && resume.photo && (
            <div className="ml-8 flex-shrink-0">
              <img
                src={resume.photo}
                alt={`${resume.firstName} ${resume.lastName}`}
                className="w-32 h-32 object-cover border-4 border-gray-600 shadow-lg"
              />
            </div>
          )}
        </div>

        {/* Contact Information - Formal layout */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-6 border-t border-gray-600">
          {resume.email && (
            <div>
              <span className="block font-semibold text-gray-400 text-xs uppercase tracking-wide mb-1">Email</span>
              <span className="text-white text-sm">{resume.email}</span>
            </div>
          )}
          {resume.phone && (
            <div>
              <span className="block font-semibold text-gray-400 text-xs uppercase tracking-wide mb-1">Phone</span>
              <span className="text-white text-sm">{resume.phone}</span>
            </div>
          )}
          {resume.website && (
            <div>
              <span className="block font-semibold text-gray-400 text-xs uppercase tracking-wide mb-1">Website</span>
              <span className="text-white text-sm">{resume.website}</span>
            </div>
          )}
          {(resume.city || resume.country) && (
            <div>
              <span className="block font-semibold text-gray-400 text-xs uppercase tracking-wide mb-1">Location</span>
              <span className="text-white text-sm">{[resume.city, resume.country].filter(Boolean).join(", ")}</span>
            </div>
          )}
        </div>
      </header>

      {/* Traditional two-column layout */}
      <div className="flex">
        {/* Left Column - Main content */}
        <div className="w-2/3 px-8 py-8 border-r border-gray-300">
          {/* Experience Section */}
          {resume.experiences && resume.experiences.length > 0 && (
            <section className="mb-10">
              <h3
                className={`text-2xl font-bold ${selectedColor.split(" ")[0]} mb-6 pb-3 border-b-2 ${selectedColor.split(" ")[1]} uppercase tracking-widest`}
              >
                Professional Experience
              </h3>
              <div className="space-y-8">
                {resume.experiences.map((experience) => (
                  <div key={experience.id} className="relative border-b border-gray-200 pb-6 last:border-b-0">
                    {/* Job Title - Most prominent */}
                    <h4 className="text-xl font-bold text-gray-900 mb-2 leading-tight uppercase">{experience.title}</h4>

                    {/* Company and Date - Traditional format */}
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h5
                          className={`text-lg font-semibold ${selectedColor.split(" ")[0]} leading-relaxed uppercase tracking-wide`}
                        >
                          {experience.company}
                        </h5>
                        {(experience.city || experience.country) && (
                          <p className="text-sm text-gray-600 mt-1 uppercase">
                            {[experience.city, experience.country].filter(Boolean).join(", ")}
                          </p>
                        )}
                      </div>
                      <div className="text-right">
                        <span className="text-base font-bold text-gray-700 uppercase">
                          {experience.startDate} - {experience.isCurrent ? "Present" : experience.endDate}
                        </span>
                      </div>
                    </div>

                    {/* Description - Formal formatting */}
                    {experience.description && (
                      <div
                        className="text-base text-gray-700 leading-relaxed prose prose-base max-w-none pl-4 border-l-2 border-gray-300"
                        dangerouslySetInnerHTML={{ __html: experience.description }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Projects Section */}
          {resume.projects && resume.projects.length > 0 && (
            <section className="mb-10">
              <h3
                className={`text-2xl font-bold ${selectedColor.split(" ")[0]} mb-6 pb-3 border-b-2 ${selectedColor.split(" ")[1]} uppercase tracking-widest`}
              >
                Key Projects
              </h3>
              <div className="space-y-6">
                {resume.projects.map((project) => (
                  <div key={project.id} className="relative border-b border-gray-200 pb-6 last:border-b-0">
                    {/* Project Title */}
                    <h4 className="text-lg font-bold text-gray-900 mb-2 leading-tight uppercase">{project.title}</h4>

                    {/* Client and Date */}
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        {project.client && (
                          <h5
                            className={`text-base font-semibold ${selectedColor.split(" ")[0]} leading-relaxed uppercase tracking-wide`}
                          >
                            {project.client}
                          </h5>
                        )}
                        {project.url && (
                          <p className="text-sm text-gray-600 mt-1">
                            <a
                              href={project.url}
                              className={`${selectedColor.split(" ")[0]} hover:underline font-semibold`}
                            >
                              {project.url}
                            </a>
                          </p>
                        )}
                      </div>
                      <span className="text-base font-bold text-gray-700 uppercase">
                        {project.startDate} - {project.endDate}
                      </span>
                    </div>

                    {/* Description */}
                    {project.description && (
                      <div
                        className="text-sm text-gray-700 leading-relaxed prose prose-sm max-w-none pl-4 border-l-2 border-gray-300"
                        dangerouslySetInnerHTML={{ __html: project.description }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>

        {/* Right Column - Traditional sidebar */}
        <div className="w-1/3 bg-gray-50 px-6 py-8">
          {/* Education Section */}
          {resume.educations && resume.educations.length > 0 && (
            <section className="mb-8">
              <h3
                className={`text-xl font-bold ${selectedColor.split(" ")[0]} mb-4 pb-2 border-b-2 ${selectedColor.split(" ")[1]} uppercase tracking-widest`}
              >
                Education
              </h3>
              <div className="space-y-4">
                {resume.educations.map((education) => (
                  <div key={education.id} className="bg-white p-4 border border-gray-300">
                    {/* Degree - Prominent */}
                    <h4 className="text-base font-bold text-gray-900 mb-1 leading-tight uppercase">
                      {education.degree}
                    </h4>

                    {/* Field of Study */}
                    {education.fieldOfStudy && (
                      <p className="text-sm font-semibold text-gray-700 mb-2">{education.fieldOfStudy}</p>
                    )}

                    {/* Institution */}
                    <h5 className={`text-sm font-semibold ${selectedColor.split(" ")[0]} mb-2 uppercase`}>
                      {education.institution}
                    </h5>

                    {/* Date and Location */}
                    <div className="text-xs text-gray-600 font-semibold uppercase">
                      <p>
                        {education.startDate} - {education.isCurrent ? "Present" : education.endDate}
                      </p>
                      {(education.city || education.country) && (
                        <p>{[education.city, education.country].filter(Boolean).join(", ")}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Skills Section - Traditional format */}
          {resume.skills && resume.skills.length > 0 && (
            <section className="mb-8">
              <h3
                className={`text-xl font-bold ${selectedColor.split(" ")[0]} mb-4 pb-2 border-b-2 ${selectedColor.split(" ")[1]} uppercase tracking-widest`}
              >
                Core Competencies
              </h3>
              <div className="space-y-3">
                {resume.skills.map((skill) => (
                  <div key={skill.id} className="bg-white p-3 border border-gray-300">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-bold text-gray-800 uppercase">{skill.name}</span>
                      {skill.proficiency && (
                        <span className="text-xs text-gray-600 font-bold">{skill.proficiency}%</span>
                      )}
                    </div>
                    {skill.proficiency && (
                      <div className="w-full bg-gray-300 h-2">
                        <div
                          className={`h-2 ${selectedColor.split(" ")[2]}`}
                          style={{ width: `${skill.proficiency}%` }}
                        ></div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Languages Section */}
          {resume.languages && resume.languages.length > 0 && (
            <section className="mb-8">
              <h3
                className={`text-xl font-bold ${selectedColor.split(" ")[0]} mb-4 pb-2 border-b-2 ${selectedColor.split(" ")[1]} uppercase tracking-widest`}
              >
                Languages
              </h3>
              <div className="space-y-3">
                {resume.languages.map((language) => (
                  <div key={language.id} className="bg-white p-3 border border-gray-300">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-bold text-gray-800 uppercase">{language.name}</span>
                      <span className="text-xs text-gray-600 font-bold">{language.proficiency}%</span>
                    </div>
                    <div className="w-full bg-gray-300 h-2">
                      <div
                        className={`h-2 ${selectedColor.split(" ")[2]}`}
                        style={{ width: `${language.proficiency}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Certifications Section */}
          {resume.certifications && resume.certifications.length > 0 && (
            <section className="mb-8">
              <h3
                className={`text-xl font-bold ${selectedColor.split(" ")[0]} mb-4 pb-2 border-b-2 ${selectedColor.split(" ")[1]} uppercase tracking-widest`}
              >
                Certifications
              </h3>
              <div className="space-y-3">
                {resume.certifications.map((cert) => (
                  <div key={cert.id} className="bg-white p-3 border border-gray-300">
                    <h4 className="text-sm font-bold text-gray-900 leading-tight mb-1 uppercase">{cert.title}</h4>
                    <p className="text-xs text-gray-600 font-semibold">{cert.issuer}</p>
                    {cert.dateReceived && <p className="text-xs text-gray-500 mt-1">{cert.dateReceived}</p>}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Awards Section */}
          {resume.awards && resume.awards.length > 0 && (
            <section className="mb-8">
              <h3
                className={`text-xl font-bold ${selectedColor.split(" ")[0]} mb-4 pb-2 border-b-2 ${selectedColor.split(" ")[1]} uppercase tracking-widest`}
              >
                Awards & Honors
              </h3>
              <div className="space-y-3">
                {resume.awards.map((award) => (
                  <div key={award.id} className="bg-white p-3 border border-gray-300">
                    <h4 className="text-sm font-bold text-gray-900 leading-tight mb-1 uppercase">{award.title}</h4>
                    <p className="text-xs text-gray-600 font-semibold">{award.issuer}</p>
                    {award.dateReceived && <p className="text-xs text-gray-500 mt-1">{award.dateReceived}</p>}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
}
