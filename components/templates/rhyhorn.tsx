import React from "react";
import SkillsDisplay from "@/components/resume/skills-display";
import { FullResume } from "@/db/schema";
import { getTemplateColorClasses, getTemplateFontClass } from "@/lib/template-utils";

// Language item component for rhyhorn template
const RhyhornLanguageItem = ({ language, colorClasses }: { language: any; colorClasses: string }) => (
  <div className="bg-white border-4 border-gray-300 p-5 shadow-lg">
    <div className="flex justify-between items-center mb-4">
      <span className="text-lg font-black text-gray-800">{language.name}</span>
      <span className="text-sm font-black text-white bg-gray-800 px-4 py-2 border-2 border-gray-400">
        {language.proficiency}%
      </span>
    </div>
    <div className="w-full bg-gray-300 h-6 border-2 border-gray-400">
      <div
        className={`h-full ${colorClasses.split(" ")[1]} border-r-2 border-gray-500`}
        style={{ width: `${language.proficiency}%` }}
      />
    </div>
  </div>
);

export default function RhyhornTemplate({ resume }: { resume: FullResume }) {
  const selectedColor = getTemplateColorClasses(resume.colorScheme);
  const fontClass = getTemplateFontClass(resume.fontFamily);

  return (
    <div
      className={`max-w-5xl mx-auto bg-white shadow-xl ${fontClass} text-gray-900 print:shadow-none border-2 border-gray-300`}
    >
      {/* Robust header with strong visual hierarchy */}
      <header className="relative bg-gradient-to-r from-gray-100 via-white to-gray-100 px-10 py-12 border-b-4 border-gray-400">
        <div className={`absolute top-0 left-0 w-full h-1 ${selectedColor.split(" ")[1]}`}></div>
        <div className={`absolute bottom-0 left-0 w-full h-1 ${selectedColor.split(" ")[1]}`}></div>

        <div className="flex items-start justify-between">
          <div className="flex-1">
            {/* Name - Strong and robust */}
            <h1 className="text-6xl font-black text-gray-900 mb-4 leading-none tracking-tight">{resume.firstName}</h1>
            <h1 className={`text-6xl font-black ${selectedColor.split(" ")[0]} mb-6 leading-none tracking-tight`}>
              {resume.lastName}
            </h1>

            {/* Job Title - Robust styling */}
            <h2 className="text-3xl font-bold text-gray-700 mb-8 leading-tight tracking-wide border-l-8 border-gray-400 pl-6">
              {resume.jobTitle}
            </h2>

            {/* Bio - Strong presentation */}
            {resume.bio && (
              <div className="bg-white border-2 border-gray-300 p-6 rounded-none shadow-lg mb-6">
                <h3 className={`text-xl font-black ${selectedColor.split(" ")[0]} mb-4 uppercase tracking-widest`}>
                  Executive Summary
                </h3>
                <p className="text-lg text-gray-700 leading-relaxed font-medium max-w-4xl">{resume.bio}</p>
              </div>
            )}
          </div>

          {/* Photo - Robust framing */}
          {resume.showPhoto && resume.photo && (
            <div className="ml-10 flex-shrink-0 relative">
              <div className={`absolute -inset-4 ${selectedColor.split(" ")[1]} opacity-20`}></div>
              <div className="absolute -inset-2 bg-white border-4 border-gray-400"></div>
              <img
                src={resume.photo}
                alt={`${resume.firstName} ${resume.lastName}`}
                className="relative w-40 h-40 object-cover border-4 border-white shadow-2xl"
              />
            </div>
          )}
        </div>

        {/* Contact Information - Robust grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-8 bg-gray-900 text-white p-6 border-4 border-gray-400">
          {resume.email && (
            <div className="text-center">
              <span className="block font-black text-gray-300 text-sm uppercase tracking-widest mb-2">Email</span>
              <span className="text-white text-base font-bold">{resume.email}</span>
            </div>
          )}
          {resume.phone && (
            <div className="text-center">
              <span className="block font-black text-gray-300 text-sm uppercase tracking-widest mb-2">Phone</span>
              <span className="text-white text-base font-bold">{resume.phone}</span>
            </div>
          )}
          {resume.website && (
            <div className="text-center">
              <span className="block font-black text-gray-300 text-sm uppercase tracking-widest mb-2">Website</span>
              <span className="text-white text-base font-bold">{resume.website}</span>
            </div>
          )}
          {(resume.city || resume.country) && (
            <div className="text-center">
              <span className="block font-black text-gray-300 text-sm uppercase tracking-widest mb-2">Location</span>
              <span className="text-white text-base font-bold">
                {[resume.city, resume.country].filter(Boolean).join(", ")}
              </span>
            </div>
          )}
        </div>
      </header>

      {/* Robust content structure */}
      <div className="flex">
        {/* Left Column - Main content with strong structure */}
        <div className="w-3/5 px-10 py-10 border-r-4 border-gray-400">
          {/* Experience Section - Robust styling */}
          {resume.experiences && resume.experiences.length > 0 && (
            <section className="mb-14">
              <div className={`bg-gray-900 text-white px-8 py-5 mb-10 border-4 ${selectedColor.split(" ")[2]}`}>
                <h3 className="text-4xl font-black uppercase tracking-widest">Professional Experience</h3>
              </div>
              <div className="space-y-12">
                {resume.experiences.map((experience, index) => (
                  <div
                    key={experience.id}
                    className="relative border-l-8 border-gray-400 pl-10 pb-8 border-b-2 border-gray-200 last:border-b-0"
                  >
                    <div
                      className={`absolute -left-6 top-0 w-12 h-12 ${selectedColor.split(" ")[1]} border-4 border-white shadow-xl flex items-center justify-center`}
                    >
                      <span className="text-white text-lg font-black">{index + 1}</span>
                    </div>

                    {/* Job Title - Most prominent */}
                    <h4 className="text-3xl font-black text-gray-900 mb-4 leading-tight">{experience.title}</h4>

                    {/* Company and Date - Robust layout */}
                    <div className="flex items-start justify-between mb-6">
                      <div>
                        <h5
                          className={`text-2xl font-black ${selectedColor.split(" ")[0]} leading-tight uppercase tracking-wide mb-2`}
                        >
                          {experience.company}
                        </h5>
                        {(experience.city || experience.country) && (
                          <p className="text-lg text-gray-600 font-bold">
                            {[experience.city, experience.country].filter(Boolean).join(", ")}
                          </p>
                        )}
                      </div>
                      <div className="text-right bg-gray-900 text-white px-6 py-3 border-4 border-gray-400">
                        <span className="text-lg font-black">
                          {experience.startDate} - {experience.isCurrent ? "Present" : experience.endDate}
                        </span>
                      </div>
                    </div>

                    {/* Description - Strong formatting */}
                    {experience.description && (
                      <div
                        className="text-lg text-gray-700 leading-relaxed prose prose-lg max-w-none bg-gray-50 p-6 border-l-4 border-gray-300"
                        dangerouslySetInnerHTML={{ __html: experience.description }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Projects Section - Robust structure */}
          {resume.projects && resume.projects.length > 0 && (
            <section className="mb-14">
              <div className={`bg-gray-900 text-white px-8 py-5 mb-10 border-4 ${selectedColor.split(" ")[2]}`}>
                <h3 className="text-4xl font-black uppercase tracking-widest">Key Projects</h3>
              </div>
              <div className="grid gap-10">
                {resume.projects.map((project) => (
                  <div
                    key={project.id}
                    className="bg-gradient-to-r from-gray-50 to-white border-4 border-gray-300 p-8 shadow-lg hover:shadow-xl transition-shadow duration-300"
                  >
                    {/* Project Title */}
                    <h4 className="text-2xl font-black text-gray-900 mb-4 leading-tight">{project.title}</h4>

                    {/* Client and Date */}
                    <div className="flex items-center justify-between mb-6">
                      {project.client && (
                        <h5
                          className={`text-xl font-black ${selectedColor.split(" ")[0]} leading-tight uppercase tracking-wide`}
                        >
                          {project.client}
                        </h5>
                      )}
                      <span className="text-lg font-black text-white bg-gray-800 px-6 py-2 border-2 border-gray-400">
                        {project.startDate} - {project.endDate}
                      </span>
                    </div>

                    {/* URL */}
                    {project.url && (
                      <p className="text-base text-gray-600 mb-4">
                        <a
                          href={project.url}
                          className={`${selectedColor.split(" ")[0]} hover:underline font-bold flex items-center text-lg`}
                        >
                          🔗 {project.url}
                        </a>
                      </p>
                    )}

                    {/* Description */}
                    {project.description && (
                      <div
                        className="text-base text-gray-700 leading-relaxed prose prose-base max-w-none"
                        dangerouslySetInnerHTML={{ __html: project.description }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>

        {/* Right Column - Robust sidebar */}
        <div className="w-2/5 bg-gradient-to-b from-gray-100 to-gray-200 px-8 py-10">
          {/* Education Section */}
          {resume.educations && resume.educations.length > 0 && (
            <section className="mb-12">
              <div className={`bg-gray-800 text-white px-6 py-4 mb-8 border-4 ${selectedColor.split(" ")[2]}`}>
                <h3 className="text-2xl font-black uppercase tracking-widest">Education</h3>
              </div>
              <div className="space-y-8">
                {resume.educations.map((education) => (
                  <div key={education.id} className="bg-white border-4 border-gray-300 p-6 shadow-lg">
                    {/* Degree - Strong emphasis */}
                    <h4 className="text-xl font-black text-gray-900 mb-3 leading-tight">{education.degree}</h4>

                    {/* Field of Study */}
                    {education.fieldOfStudy && (
                      <p className="text-lg font-bold text-gray-700 mb-3">{education.fieldOfStudy}</p>
                    )}

                    {/* Institution */}
                    <h5 className={`text-lg font-black ${selectedColor.split(" ")[0]} mb-4 uppercase`}>
                      {education.institution}
                    </h5>

                    {/* Date and Location */}
                    <div className="text-sm text-gray-600 font-bold bg-gray-100 p-3 border-2 border-gray-300">
                      <p className="mb-1">
                        {education.startDate} - {education.isCurrent ? "Present" : education.endDate}
                      </p>
                      {(education.city || education.country) && (
                        <p>{[education.city, education.country].filter(Boolean).join(", ")}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Skills Section - Robust visualization */}
          {resume.skills && resume.skills.length > 0 && (
            <section className="mb-12">
              <div className={`bg-gray-800 text-white px-6 py-4 mb-8 border-4 ${selectedColor.split(" ")[2]}`}>
                <h3 className="text-2xl font-black uppercase tracking-widest">Core Skills</h3>
              </div>
              <SkillsDisplay
                skills={resume.skills}
                colorScheme={resume.colorScheme}
                variant="progress-bars"
                groupByCategory={true}
                maxColumns={1}
                showProficiency={true}
              />
            </section>
          )}

          {/* Languages Section */}
          {resume.languages && resume.languages.length > 0 && (
            <section className="mb-12">
              <div className={`bg-gray-800 text-white px-6 py-4 mb-8 border-4 ${selectedColor.split(" ")[2]}`}>
                <h3 className="text-2xl font-black uppercase tracking-widest">Languages</h3>
              </div>
              <div className="space-y-6">
                {resume.languages.map((language) => (
                  <RhyhornLanguageItem
                    key={language.id}
                    language={language}
                    colorClasses={selectedColor}
                  />
                ))}
              </div>
            </section>
          )}

          {/* Compact sections with robust styling */}
          {resume.certifications && resume.certifications.length > 0 && (
            <section className="mb-10">
              <div className={`bg-gray-700 text-white px-5 py-3 mb-6 border-4 ${selectedColor.split(" ")[2]}`}>
                <h3 className="text-lg font-black uppercase tracking-widest">Certifications</h3>
              </div>
              <div className="space-y-4">
                {resume.certifications.map((cert) => (
                  <div key={cert.id} className="bg-white border-3 border-gray-300 p-4 shadow-md">
                    <h4 className="text-base font-black text-gray-900 leading-tight mb-2">{cert.title}</h4>
                    <p className="text-sm text-gray-600 font-bold">{cert.issuer}</p>
                    {cert.dateReceived && (
                      <p className="text-xs text-gray-500 mt-1 font-semibold">{cert.dateReceived}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {resume.awards && resume.awards.length > 0 && (
            <section className="mb-10">
              <div className={`bg-gray-700 text-white px-5 py-3 mb-6 border-4 ${selectedColor.split(" ")[2]}`}>
                <h3 className="text-lg font-black uppercase tracking-widest">Awards & Honors</h3>
              </div>
              <div className="space-y-4">
                {resume.awards.map((award) => (
                  <div key={award.id} className="bg-white border-3 border-gray-300 p-4 shadow-md">
                    <h4 className="text-base font-black text-gray-900 leading-tight mb-2">{award.title}</h4>
                    <p className="text-sm text-gray-600 font-bold">{award.issuer}</p>
                    {award.dateReceived && (
                      <p className="text-xs text-gray-500 mt-1 font-semibold">{award.dateReceived}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
}
