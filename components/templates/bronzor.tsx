import React from "react";
import SkillsDisplay from "@/components/resume/skills-display";
import { FullResume } from "@/db/schema";

interface BronzorTemplateProps {
  resume: FullResume;
}

export default function BronzorTemplate({ resume }: BronzorTemplateProps) {
  const colorClasses = {
    blue: "text-blue-800 border-blue-800",
    green: "text-green-800 border-green-800",
    purple: "text-purple-800 border-purple-800",
    red: "text-red-800 border-red-800",
    orange: "text-orange-800 border-orange-800",
    teal: "text-teal-800 border-teal-800",
    pink: "text-pink-800 border-pink-800",
    indigo: "text-indigo-800 border-indigo-800",
  };

  const selectedColor = colorClasses[resume.colorScheme as keyof typeof colorClasses] || colorClasses.blue;

  return (
    <div
      className={`max-w-4xl mx-auto bg-white shadow-sm font-${resume.fontFamily || "inter"} text-gray-900 print:shadow-none`}
    >
      {/* Header Section - Traditional centered layout */}
      <header className="text-center px-8 py-8 border-b-2 border-gray-300">
        {/* Photo */}
        {resume.showPhoto && resume.photo && (
          <div className="mb-6">
            <img
              src={resume.photo}
              alt={`${resume.firstName} ${resume.lastName}`}
              className="w-28 h-28 rounded-full object-cover mx-auto border-4 border-gray-300 shadow-sm"
            />
          </div>
        )}

        {/* Name - Most prominent, traditional */}
        <h1 className="text-5xl font-bold text-gray-900 mb-3 leading-tight tracking-wide uppercase">
          {resume.firstName} {resume.lastName}
        </h1>

        {/* Job Title - Traditional underline styling */}
        <h2
          className={`text-xl font-semibold ${selectedColor.split(" ")[0]} mb-4 leading-relaxed border-b-2 ${selectedColor.split(" ")[1]} inline-block pb-1`}
        >
          {resume.jobTitle}
        </h2>

        {/* Contact Information - Traditional horizontal layout */}
        <div className="flex flex-wrap justify-center items-center gap-4 text-sm font-medium text-gray-700 mb-6">
          {resume.email && <span className="flex items-center">📧 {resume.email}</span>}
          {resume.phone && <span className="flex items-center">📞 {resume.phone}</span>}
          {resume.website && <span className="flex items-center">🌐 {resume.website}</span>}
          {(resume.city || resume.country) && (
            <span className="flex items-center">📍 {[resume.city, resume.country].filter(Boolean).join(", ")}</span>
          )}
        </div>

        {/* Bio - Professional summary */}
        {resume.bio && (
          <div className="max-w-3xl mx-auto">
            <h3 className={`text-lg font-semibold ${selectedColor.split(" ")[0]} mb-3 uppercase tracking-wide`}>
              Professional Summary
            </h3>
            <p className="text-base text-gray-700 leading-relaxed text-justify">{resume.bio}</p>
          </div>
        )}
      </header>

      {/* Content Sections - Single column, traditional */}
      <div className="px-8 py-6">
        {/* Experience Section */}
        {resume.experiences && resume.experiences.length > 0 && (
          <section className="mb-10">
            <h3
              className={`text-2xl font-bold ${selectedColor.split(" ")[0]} mb-6 pb-2 border-b-2 ${selectedColor.split(" ")[1]} uppercase tracking-wide text-center`}
            >
              Professional Experience
            </h3>
            <div className="space-y-8">
              {resume.experiences.map((experience) => (
                <div key={experience.id} className="border-l-4 border-gray-300 pl-6 relative">
                  <div
                    className={`absolute -left-2 top-0 w-4 h-4 ${selectedColor.split(" ")[0].replace("text-", "bg-")} transform rotate-45`}
                  ></div>

                  {/* Job Title and Company - Traditional format */}
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="text-xl font-bold text-gray-900 leading-tight">{experience.title}</h4>
                      <h5 className={`text-lg font-semibold ${selectedColor.split(" ")[0]} leading-relaxed`}>
                        {experience.company}
                      </h5>
                    </div>
                    <div className="text-right">
                      <span className="text-base font-semibold text-gray-700">
                        {experience.startDate} - {experience.isCurrent ? "Present" : experience.endDate}
                      </span>
                      {(experience.city || experience.country) && (
                        <p className="text-sm text-gray-600 mt-1">
                          {[experience.city, experience.country].filter(Boolean).join(", ")}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Description - Well-formatted for traditional resumes */}
                  {experience.description && (
                    <div
                      className="text-base text-gray-700 leading-relaxed prose prose-base max-w-none mt-4"
                      dangerouslySetInnerHTML={{ __html: experience.description }}
                    />
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Education Section */}
        {resume.educations && resume.educations.length > 0 && (
          <section className="mb-10">
            <h3
              className={`text-2xl font-bold ${selectedColor.split(" ")[0]} mb-6 pb-2 border-b-2 ${selectedColor.split(" ")[1]} uppercase tracking-wide text-center`}
            >
              Education
            </h3>
            <div className="space-y-6">
              {resume.educations.map((education) => (
                <div key={education.id} className="border-l-4 border-gray-300 pl-6 relative">
                  <div
                    className={`absolute -left-2 top-0 w-4 h-4 ${selectedColor.split(" ")[0].replace("text-", "bg-")} transform rotate-45`}
                  ></div>

                  <div className="flex justify-between items-start mb-2">
                    <div>
                      {/* Degree - Most prominent */}
                      <h4 className="text-lg font-bold text-gray-900 leading-tight">{education.degree}</h4>

                      {/* Field of Study */}
                      {education.fieldOfStudy && (
                        <p className="text-base font-semibold text-gray-700 mb-1">{education.fieldOfStudy}</p>
                      )}

                      {/* Institution */}
                      <h5 className={`text-base font-semibold ${selectedColor.split(" ")[0]} leading-relaxed`}>
                        {education.institution}
                      </h5>
                    </div>

                    <div className="text-right">
                      <span className="text-base font-semibold text-gray-700">
                        {education.startDate} - {education.isCurrent ? "Present" : education.endDate}
                      </span>
                      {(education.city || education.country) && (
                        <p className="text-sm text-gray-600 mt-1">
                          {[education.city, education.country].filter(Boolean).join(", ")}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Description */}
                  {education.description && (
                    <div
                      className="text-sm text-gray-700 leading-relaxed mt-3"
                      dangerouslySetInnerHTML={{ __html: education.description }}
                    />
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Skills Section - Traditional format */}
        {resume.skills && resume.skills.length > 0 && (
          <section className="mb-10">
            <h3
              className={`text-2xl font-bold ${selectedColor.split(" ")[0]} mb-6 pb-2 border-b-2 ${selectedColor.split(" ")[1]} uppercase tracking-wide text-center`}
            >
              Technical Skills
            </h3>
            <SkillsDisplay
              skills={resume.skills}
              colorScheme={resume.colorScheme}
              variant="progress-bars"
              groupByCategory={true}
              maxColumns={2}
              showProficiency={true}
            />
          </section>
        )}

        {/* Projects Section */}
        {resume.projects && resume.projects.length > 0 && (
          <section className="mb-10">
            <h3
              className={`text-2xl font-bold ${selectedColor.split(" ")[0]} mb-6 pb-2 border-b-2 ${selectedColor.split(" ")[1]} uppercase tracking-wide text-center`}
            >
              Key Projects
            </h3>
            <div className="space-y-6">
              {resume.projects.map((project) => (
                <div key={project.id} className="border-l-4 border-gray-300 pl-6 relative">
                  <div
                    className={`absolute -left-2 top-0 w-4 h-4 ${selectedColor.split(" ")[0].replace("text-", "bg-")} transform rotate-45`}
                  ></div>

                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="text-lg font-bold text-gray-900 leading-tight">{project.title}</h4>
                      {project.client && (
                        <h5 className={`text-base font-semibold ${selectedColor.split(" ")[0]} leading-relaxed`}>
                          {project.client}
                        </h5>
                      )}
                    </div>
                    <span className="text-base font-semibold text-gray-700">
                      {project.startDate} - {project.endDate}
                    </span>
                  </div>

                  {/* URL */}
                  {project.url && (
                    <p className="text-sm text-gray-600 mb-3">
                      <a href={project.url} className={`${selectedColor.split(" ")[0]} hover:underline font-medium`}>
                        {project.url}
                      </a>
                    </p>
                  )}

                  {/* Description */}
                  {project.description && (
                    <div
                      className="text-base text-gray-700 leading-relaxed prose prose-base max-w-none"
                      dangerouslySetInnerHTML={{ __html: project.description }}
                    />
                  )}
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Additional Sections in Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Languages */}
          {resume.languages && resume.languages.length > 0 && (
            <section className="mb-8">
              <h3
                className={`text-xl font-bold ${selectedColor.split(" ")[0]} mb-4 pb-2 border-b ${selectedColor.split(" ")[1]} uppercase tracking-wide`}
              >
                Languages
              </h3>
              <div className="space-y-3">
                {resume.languages.map((language) => (
                  <div key={language.id} className="flex justify-between items-center">
                    <span className="text-base font-medium text-gray-800">{language.name}</span>
                    <div className="flex items-center">
                      <div className="w-20 bg-gray-200 rounded-full h-2 mr-2">
                        <div
                          className={`h-2 rounded-full ${selectedColor.split(" ")[0].replace("text-", "bg-")}`}
                          style={{ width: `${language.proficiency}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-600 font-medium">{language.proficiency}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Certifications */}
          {resume.certifications && resume.certifications.length > 0 && (
            <section className="mb-8">
              <h3
                className={`text-xl font-bold ${selectedColor.split(" ")[0]} mb-4 pb-2 border-b ${selectedColor.split(" ")[1]} uppercase tracking-wide`}
              >
                Certifications
              </h3>
              <div className="space-y-3">
                {resume.certifications.map((cert) => (
                  <div key={cert.id}>
                    <h4 className="text-base font-semibold text-gray-900 leading-tight">{cert.title}</h4>
                    <p className="text-sm text-gray-600">{cert.issuer}</p>
                    {cert.dateReceived && <p className="text-xs text-gray-500">{cert.dateReceived}</p>}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
}
