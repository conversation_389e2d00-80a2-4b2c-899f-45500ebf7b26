import React from "react";
import { FullResume } from "@/db/schema";
import { getTemplateColorClasses, getTemplateFontClass } from "@/lib/template-utils";

interface GlalieTemplateProps {
  resume: FullResume;
}

export default function GlalieTemplate({ resume }: GlalieTemplateProps) {
  const selectedColor = getTemplateColorClasses(resume.colorScheme);
  const fontClass = getTemplateFontClass(resume.fontFamily);

  return (
    <div
      className={`max-w-4xl mx-auto bg-white shadow-sm ${fontClass} text-gray-900 print:shadow-none`}
    >
      {/* Professional Header */}
      <header className="bg-gray-900 text-white px-8 py-10">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            {/* Name - Corporate styling */}
            <h1 className="text-4xl font-bold text-white mb-2 leading-tight tracking-wide uppercase">
              {resume.firstName} {resume.lastName}
            </h1>

            {/* Job Title - Professional accent */}
            <h2
              className={`text-xl font-semibold mb-4 leading-relaxed ${selectedColor.split(" ")[0].replace("text-", "text-").replace("700", "300")}`}
            >
              {resume.jobTitle}
            </h2>

            {/* Professional Summary */}
            {resume.bio && (
              <div className="mb-6">
                <h3 className="text-lg font-semibold text-gray-300 mb-3 uppercase tracking-wide">Executive Summary</h3>
                <p className="text-base text-gray-200 leading-relaxed max-w-3xl">{resume.bio}</p>
              </div>
            )}
          </div>

          {/* Photo - Professional styling */}
          {resume.showPhoto && resume.photo && (
            <div className="ml-8 flex-shrink-0">
              <img
                src={resume.photo}
                alt={`${resume.firstName} ${resume.lastName}`}
                className="w-32 h-32 object-cover border-4 border-gray-700 shadow-lg"
              />
            </div>
          )}
        </div>

        {/* Contact Information - Professional layout */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-6 border-t border-gray-700">
          {resume.email && (
            <div className="flex items-center">
              <span className="font-semibold text-gray-400 text-sm">EMAIL:</span>
              <span className="ml-2 text-white text-sm">{resume.email}</span>
            </div>
          )}
          {resume.phone && (
            <div className="flex items-center">
              <span className="font-semibold text-gray-400 text-sm">PHONE:</span>
              <span className="ml-2 text-white text-sm">{resume.phone}</span>
            </div>
          )}
          {resume.website && (
            <div className="flex items-center">
              <span className="font-semibold text-gray-400 text-sm">WEB:</span>
              <span className="ml-2 text-white text-sm">{resume.website}</span>
            </div>
          )}
          {(resume.city || resume.country) && (
            <div className="flex items-center">
              <span className="font-semibold text-gray-400 text-sm">LOCATION:</span>
              <span className="ml-2 text-white text-sm">
                {[resume.city, resume.country].filter(Boolean).join(", ")}
              </span>
            </div>
          )}
        </div>
      </header>

      {/* Two-column professional layout */}
      <div className="flex">
        {/* Left Column - Main content */}
        <div className="w-2/3 px-8 py-8">
          {/* Experience Section */}
          {resume.experiences && resume.experiences.length > 0 && (
            <section className="mb-10">
              <h3
                className={`text-2xl font-bold ${selectedColor.split(" ")[0]} mb-6 pb-3 border-b-3 ${selectedColor.split(" ")[2]} uppercase tracking-wide`}
              >
                Professional Experience
              </h3>
              <div className="space-y-8">
                {resume.experiences.map((experience) => (
                  <div key={experience.id} className="relative">
                    {/* Job Title - Most prominent */}
                    <h4 className="text-xl font-bold text-gray-900 mb-2 leading-tight">{experience.title}</h4>

                    {/* Company and Date - Professional format */}
                    <div className="flex items-center justify-between mb-3">
                      <h5
                        className={`text-lg font-semibold ${selectedColor.split(" ")[0]} leading-relaxed uppercase tracking-wide`}
                      >
                        {experience.company}
                      </h5>
                      <div className="text-right">
                        <span className="text-base font-bold text-gray-700">
                          {experience.startDate} - {experience.isCurrent ? "PRESENT" : experience.endDate}
                        </span>
                        {(experience.city || experience.country) && (
                          <p className="text-sm text-gray-600 mt-1">
                            {[experience.city, experience.country].filter(Boolean).join(", ")}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Description - Professional formatting */}
                    {experience.description && (
                      <div
                        className="text-base text-gray-700 leading-relaxed prose prose-base max-w-none pl-4 border-l-2 border-gray-200"
                        dangerouslySetInnerHTML={{ __html: experience.description }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Projects Section */}
          {resume.projects && resume.projects.length > 0 && (
            <section className="mb-10">
              <h3
                className={`text-2xl font-bold ${selectedColor.split(" ")[0]} mb-6 pb-3 border-b-3 ${selectedColor.split(" ")[2]} uppercase tracking-wide`}
              >
                Key Projects
              </h3>
              <div className="space-y-6">
                {resume.projects.map((project) => (
                  <div key={project.id} className="relative">
                    {/* Project Title */}
                    <h4 className="text-lg font-bold text-gray-900 mb-2 leading-tight">{project.title}</h4>

                    {/* Client and Date */}
                    <div className="flex items-center justify-between mb-3">
                      {project.client && (
                        <h5
                          className={`text-base font-semibold ${selectedColor.split(" ")[0]} leading-relaxed uppercase tracking-wide`}
                        >
                          {project.client}
                        </h5>
                      )}
                      <span className="text-base font-bold text-gray-700">
                        {project.startDate} - {project.endDate}
                      </span>
                    </div>

                    {/* URL */}
                    {project.url && (
                      <p className="text-sm text-gray-600 mb-3">
                        <a
                          href={project.url}
                          className={`${selectedColor.split(" ")[0]} hover:underline font-semibold`}
                        >
                          {project.url}
                        </a>
                      </p>
                    )}

                    {/* Description */}
                    {project.description && (
                      <div
                        className="text-sm text-gray-700 leading-relaxed prose prose-sm max-w-none pl-4 border-l-2 border-gray-200"
                        dangerouslySetInnerHTML={{ __html: project.description }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>

        {/* Right Column - Professional sidebar */}
        <div className="w-1/3 bg-gray-100 px-6 py-8 border-l border-gray-300">
          {/* Education Section */}
          {resume.educations && resume.educations.length > 0 && (
            <section className="mb-8">
              <h3
                className={`text-xl font-bold ${selectedColor.split(" ")[0]} mb-4 pb-2 border-b-2 ${selectedColor.split(" ")[2]} uppercase tracking-wide`}
              >
                Education
              </h3>
              <div className="space-y-4">
                {resume.educations.map((education) => (
                  <div key={education.id} className="bg-white p-4 shadow-sm">
                    {/* Degree - Prominent */}
                    <h4 className="text-base font-bold text-gray-900 mb-1 leading-tight uppercase">
                      {education.degree}
                    </h4>

                    {/* Field of Study */}
                    {education.fieldOfStudy && (
                      <p className="text-sm font-semibold text-gray-700 mb-2">{education.fieldOfStudy}</p>
                    )}

                    {/* Institution */}
                    <h5 className={`text-sm font-semibold ${selectedColor.split(" ")[0]} mb-2 uppercase`}>
                      {education.institution}
                    </h5>

                    {/* Date and Location */}
                    <div className="text-xs text-gray-600 font-semibold">
                      <p>
                        {education.startDate} - {education.isCurrent ? "PRESENT" : education.endDate}
                      </p>
                      {(education.city || education.country) && (
                        <p>{[education.city, education.country].filter(Boolean).join(", ")}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Skills Section - Corporate format */}
          {resume.skills && resume.skills.length > 0 && (
            <section className="mb-8">
              <h3
                className={`text-xl font-bold ${selectedColor.split(" ")[0]} mb-4 pb-2 border-b-2 ${selectedColor.split(" ")[2]} uppercase tracking-wide`}
              >
                Core Competencies
              </h3>
              <div className="space-y-3">
                {resume.skills.map((skill) => (
                  <div key={skill.id} className="bg-white p-3 shadow-sm">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-bold text-gray-800 uppercase">{skill.name}</span>
                      {skill.proficiency && (
                        <span className="text-xs text-gray-600 font-bold">{skill.proficiency}%</span>
                      )}
                    </div>
                    {skill.proficiency && (
                      <div className="w-full bg-gray-300 h-2">
                        <div
                          className={`h-2 ${selectedColor.split(" ")[1]}`}
                          style={{ width: `${skill.proficiency}%` }}
                        ></div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Languages Section */}
          {resume.languages && resume.languages.length > 0 && (
            <section className="mb-8">
              <h3
                className={`text-xl font-bold ${selectedColor.split(" ")[0]} mb-4 pb-2 border-b-2 ${selectedColor.split(" ")[2]} uppercase tracking-wide`}
              >
                Languages
              </h3>
              <div className="space-y-3">
                {resume.languages.map((language) => (
                  <div key={language.id} className="bg-white p-3 shadow-sm">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-bold text-gray-800 uppercase">{language.name}</span>
                      <span className="text-xs text-gray-600 font-bold">{language.proficiency}%</span>
                    </div>
                    <div className="w-full bg-gray-300 h-2">
                      <div
                        className={`h-2 ${selectedColor.split(" ")[1]}`}
                        style={{ width: `${language.proficiency}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Certifications Section */}
          {resume.certifications && resume.certifications.length > 0 && (
            <section className="mb-8">
              <h3
                className={`text-xl font-bold ${selectedColor.split(" ")[0]} mb-4 pb-2 border-b-2 ${selectedColor.split(" ")[2]} uppercase tracking-wide`}
              >
                Certifications
              </h3>
              <div className="space-y-3">
                {resume.certifications.map((cert) => (
                  <div key={cert.id} className="bg-white p-3 shadow-sm">
                    <h4 className="text-sm font-bold text-gray-900 leading-tight mb-1 uppercase">{cert.title}</h4>
                    <p className="text-xs text-gray-600 font-semibold">{cert.issuer}</p>
                    {cert.dateReceived && <p className="text-xs text-gray-500 mt-1">{cert.dateReceived}</p>}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
}
