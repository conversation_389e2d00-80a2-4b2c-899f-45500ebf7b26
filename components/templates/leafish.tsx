import React from "react";
import { FullResume } from "@/db/schema";

interface LeafishTemplateProps {
  resume: FullResume;
}

export default function LeafishTemplate({ resume }: LeafishTemplateProps) {
  const colorClasses = {
    blue: "text-blue-600 bg-blue-600 border-blue-600 bg-blue-100",
    green: "text-green-600 bg-green-600 border-green-600 bg-green-100",
    purple: "text-purple-600 bg-purple-600 border-purple-600 bg-purple-100",
    red: "text-red-600 bg-red-600 border-red-600 bg-red-100",
    orange: "text-orange-600 bg-orange-600 border-orange-600 bg-orange-100",
    teal: "text-teal-600 bg-teal-600 border-teal-600 bg-teal-100",
    pink: "text-pink-600 bg-pink-600 border-pink-600 bg-pink-100",
    indigo: "text-indigo-600 bg-indigo-600 border-indigo-600 bg-indigo-100",
  };

  const selectedColor = colorClasses[resume.colorScheme as keyof typeof colorClasses] || colorClasses.green;

  return (
    <div
      className={`max-w-4xl mx-auto bg-white shadow-sm font-${resume.fontFamily || "inter"} text-gray-900 print:shadow-none`}
    >
      {/* Nature-inspired header */}
      <header className="relative px-8 py-10 bg-gradient-to-r from-green-50 to-blue-50 border-b border-green-200">
        <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
          <svg viewBox="0 0 100 100" className="w-full h-full fill-current text-green-400">
            <path d="M50 10 C30 10, 10 30, 10 50 C10 70, 30 90, 50 90 C70 90, 90 70, 90 50 C90 30, 70 10, 50 10 Z M25 35 L75 35 L75 65 L25 65 Z" />
          </svg>
        </div>

        <div className="relative flex items-start justify-between">
          <div className="flex-1">
            {/* Name - Nature-inspired styling */}
            <h1 className="text-4xl font-bold text-gray-800 mb-3 leading-tight">
              {resume.firstName} <span className={`${selectedColor.split(" ")[0]}`}>{resume.lastName}</span>
            </h1>

            {/* Job Title - Organic feel */}
            <h2 className={`text-xl font-semibold ${selectedColor.split(" ")[0]} mb-4 leading-relaxed`}>
              {resume.jobTitle}
            </h2>

            {/* Bio - Natural flow */}
            {resume.bio && (
              <p className="text-base text-gray-700 leading-relaxed max-w-3xl mb-6 bg-white bg-opacity-70 p-4 rounded-lg">
                {resume.bio}
              </p>
            )}
          </div>

          {/* Photo - Organic border */}
          {resume.showPhoto && resume.photo && (
            <div className="ml-8 flex-shrink-0 relative">
              <div
                className={`absolute inset-0 ${selectedColor.split(" ")[1]} opacity-20 rounded-3xl transform rotate-3`}
              ></div>
              <img
                src={resume.photo}
                alt={`${resume.firstName} ${resume.lastName}`}
                className="relative w-32 h-32 rounded-3xl object-cover border-4 border-white shadow-lg"
              />
            </div>
          )}
        </div>

        {/* Contact - Flowing layout */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 bg-white bg-opacity-50 p-4 rounded-lg">
          {resume.email && (
            <div className="flex items-center">
              <span className="text-green-600 mr-2">🌿</span>
              <span className="text-sm font-medium text-gray-700">{resume.email}</span>
            </div>
          )}
          {resume.phone && (
            <div className="flex items-center">
              <span className="text-blue-600 mr-2">💧</span>
              <span className="text-sm font-medium text-gray-700">{resume.phone}</span>
            </div>
          )}
          {resume.website && (
            <div className="flex items-center">
              <span className="text-yellow-600 mr-2">☀️</span>
              <span className="text-sm font-medium text-gray-700">{resume.website}</span>
            </div>
          )}
          {(resume.city || resume.country) && (
            <div className="flex items-center">
              <span className="text-brown-600 mr-2">🌍</span>
              <span className="text-sm font-medium text-gray-700">
                {[resume.city, resume.country].filter(Boolean).join(", ")}
              </span>
            </div>
          )}
        </div>
      </header>

      {/* Asymmetric layout - Creative flow */}
      <div className="flex">
        {/* Left Column - Main content with organic shapes */}
        <div className="w-3/5 px-8 py-8">
          {/* Experience Section */}
          {resume.experiences && resume.experiences.length > 0 && (
            <section className="mb-12">
              <div className="flex items-center mb-8">
                <div
                  className={`w-8 h-8 ${selectedColor.split(" ")[1]} rounded-full mr-4 flex items-center justify-center`}
                >
                  <span className="text-white text-sm font-bold">🌱</span>
                </div>
                <h3 className="text-3xl font-bold text-gray-800 tracking-wide">Growth & Experience</h3>
              </div>

              <div className="space-y-10">
                {resume.experiences.map((experience, index) => (
                  <div key={experience.id} className="relative">
                    {/* Organic timeline */}
                    <div
                      className={`absolute -left-6 top-0 w-6 h-6 ${selectedColor.split(" ")[1]} rounded-full border-4 border-white shadow-md`}
                    ></div>
                    <div
                      className={`absolute -left-4 top-6 bottom-0 w-1 ${selectedColor.split(" ")[3]} opacity-50`}
                    ></div>

                    <div className="pl-4 bg-gradient-to-r from-green-50 to-transparent p-6 rounded-r-3xl border-l-4 border-green-200">
                      {/* Job Title - Prominent */}
                      <h4 className="text-2xl font-bold text-gray-900 mb-3 leading-tight">{experience.title}</h4>

                      {/* Company and Date - Organic layout */}
                      <div className="flex items-center justify-between mb-4">
                        <h5 className={`text-xl font-semibold ${selectedColor.split(" ")[0]} leading-relaxed`}>
                          {experience.company}
                        </h5>
                        <div className="text-right bg-white px-4 py-2 rounded-full shadow-sm">
                          <span className="text-base font-bold text-gray-700">
                            {experience.startDate} - {experience.isCurrent ? "Present" : experience.endDate}
                          </span>
                          {(experience.city || experience.country) && (
                            <p className="text-sm text-gray-600 mt-1">
                              {[experience.city, experience.country].filter(Boolean).join(", ")}
                            </p>
                          )}
                        </div>
                      </div>

                      {/* Description - Natural flow */}
                      {experience.description && (
                        <div
                          className="text-base text-gray-700 leading-relaxed prose prose-base max-w-none bg-white bg-opacity-60 p-4 rounded-2xl"
                          dangerouslySetInnerHTML={{ __html: experience.description }}
                        />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Projects Section */}
          {resume.projects && resume.projects.length > 0 && (
            <section className="mb-12">
              <div className="flex items-center mb-8">
                <div
                  className={`w-8 h-8 ${selectedColor.split(" ")[1]} rounded-full mr-4 flex items-center justify-center`}
                >
                  <span className="text-white text-sm font-bold">🌿</span>
                </div>
                <h3 className="text-3xl font-bold text-gray-800 tracking-wide">Creative Projects</h3>
              </div>

              <div className="grid gap-8">
                {resume.projects.map((project) => (
                  <div
                    key={project.id}
                    className="bg-gradient-to-br from-white to-green-50 p-6 rounded-3xl border border-green-200 shadow-sm hover:shadow-md transition-shadow duration-300"
                  >
                    {/* Project Title */}
                    <h4 className="text-xl font-bold text-gray-900 mb-3 leading-tight">{project.title}</h4>

                    {/* Client and Date */}
                    <div className="flex items-center justify-between mb-4">
                      {project.client && (
                        <h5 className={`text-lg font-semibold ${selectedColor.split(" ")[0]} leading-relaxed`}>
                          {project.client}
                        </h5>
                      )}
                      <span className="text-sm font-bold text-gray-700 bg-white px-3 py-1 rounded-full">
                        {project.startDate} - {project.endDate}
                      </span>
                    </div>

                    {/* URL */}
                    {project.url && (
                      <p className="text-sm text-gray-600 mb-4">
                        <a
                          href={project.url}
                          className={`${selectedColor.split(" ")[0]} hover:underline font-medium flex items-center`}
                        >
                          🌐 {project.url}
                        </a>
                      </p>
                    )}

                    {/* Description */}
                    {project.description && (
                      <div
                        className="text-sm text-gray-700 leading-relaxed prose prose-sm max-w-none"
                        dangerouslySetInnerHTML={{ __html: project.description }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>

        {/* Right Column - Sidebar with natural feel */}
        <div className="w-2/5 bg-gradient-to-b from-green-50 to-blue-50 px-6 py-8 border-l border-green-200">
          {/* Education Section */}
          {resume.educations && resume.educations.length > 0 && (
            <section className="mb-10">
              <div className="flex items-center mb-6">
                <div
                  className={`w-6 h-6 ${selectedColor.split(" ")[1]} rounded-full mr-3 flex items-center justify-center`}
                >
                  <span className="text-white text-xs font-bold">🎓</span>
                </div>
                <h3 className="text-xl font-bold text-gray-800 tracking-wide">Education</h3>
              </div>

              <div className="space-y-6">
                {resume.educations.map((education) => (
                  <div key={education.id} className="bg-white p-5 rounded-2xl shadow-sm border border-green-200">
                    {/* Degree - Prominent */}
                    <h4 className="text-lg font-bold text-gray-900 mb-2 leading-tight">{education.degree}</h4>

                    {/* Field of Study */}
                    {education.fieldOfStudy && (
                      <p className="text-base font-semibold text-gray-700 mb-2">{education.fieldOfStudy}</p>
                    )}

                    {/* Institution */}
                    <h5 className={`text-base font-semibold ${selectedColor.split(" ")[0]} mb-3`}>
                      {education.institution}
                    </h5>

                    {/* Date and Location */}
                    <div className="text-sm text-gray-600 space-y-1">
                      <p className="font-medium">
                        {education.startDate} - {education.isCurrent ? "Present" : education.endDate}
                      </p>
                      {(education.city || education.country) && (
                        <p>📍 {[education.city, education.country].filter(Boolean).join(", ")}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Skills Section - Organic bars */}
          {resume.skills && resume.skills.length > 0 && (
            <section className="mb-10">
              <div className="flex items-center mb-6">
                <div
                  className={`w-6 h-6 ${selectedColor.split(" ")[1]} rounded-full mr-3 flex items-center justify-center`}
                >
                  <span className="text-white text-xs font-bold">⚡</span>
                </div>
                <h3 className="text-xl font-bold text-gray-800 tracking-wide">Skills</h3>
              </div>

              <div className="space-y-4">
                {resume.skills.map((skill) => (
                  <div key={skill.id} className="bg-white p-4 rounded-2xl shadow-sm border border-green-200">
                    <div className="flex justify-between items-center mb-3">
                      <span className="text-base font-semibold text-gray-800">{skill.name}</span>
                      {skill.proficiency && (
                        <span className="text-sm font-bold text-white bg-green-600 px-3 py-1 rounded-full">
                          {skill.proficiency}%
                        </span>
                      )}
                    </div>
                    {skill.proficiency && (
                      <div className="w-full bg-green-100 rounded-full h-3">
                        <div
                          className={`h-3 rounded-full ${selectedColor.split(" ")[1]} shadow-sm`}
                          style={{ width: `${skill.proficiency}%` }}
                        ></div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Languages Section */}
          {resume.languages && resume.languages.length > 0 && (
            <section className="mb-10">
              <div className="flex items-center mb-6">
                <div
                  className={`w-6 h-6 ${selectedColor.split(" ")[1]} rounded-full mr-3 flex items-center justify-center`}
                >
                  <span className="text-white text-xs font-bold">🌍</span>
                </div>
                <h3 className="text-xl font-bold text-gray-800 tracking-wide">Languages</h3>
              </div>

              <div className="space-y-4">
                {resume.languages.map((language) => (
                  <div key={language.id} className="bg-white p-4 rounded-2xl shadow-sm border border-green-200">
                    <div className="flex justify-between items-center mb-3">
                      <span className="text-base font-semibold text-gray-800">{language.name}</span>
                      <span className="text-sm font-bold text-white bg-blue-600 px-3 py-1 rounded-full">
                        {language.proficiency}%
                      </span>
                    </div>
                    <div className="w-full bg-blue-100 rounded-full h-3">
                      <div
                        className="h-3 rounded-full bg-blue-600 shadow-sm"
                        style={{ width: `${language.proficiency}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Compact sections */}
          {resume.certifications && resume.certifications.length > 0 && (
            <section className="mb-8">
              <div className="flex items-center mb-4">
                <div
                  className={`w-5 h-5 ${selectedColor.split(" ")[1]} rounded-full mr-3 flex items-center justify-center`}
                >
                  <span className="text-white text-xs font-bold">🏆</span>
                </div>
                <h3 className="text-lg font-bold text-gray-800 tracking-wide">Certifications</h3>
              </div>
              <div className="space-y-3">
                {resume.certifications.map((cert) => (
                  <div key={cert.id} className="bg-white p-3 rounded-xl shadow-sm border border-green-200">
                    <h4 className="text-sm font-bold text-gray-900 leading-tight mb-1">{cert.title}</h4>
                    <p className="text-xs text-gray-600 font-medium">{cert.issuer}</p>
                    {cert.dateReceived && <p className="text-xs text-gray-500 mt-1">{cert.dateReceived}</p>}
                  </div>
                ))}
              </div>
            </section>
          )}

          {resume.awards && resume.awards.length > 0 && (
            <section className="mb-8">
              <div className="flex items-center mb-4">
                <div
                  className={`w-5 h-5 ${selectedColor.split(" ")[1]} rounded-full mr-3 flex items-center justify-center`}
                >
                  <span className="text-white text-xs font-bold">⭐</span>
                </div>
                <h3 className="text-lg font-bold text-gray-800 tracking-wide">Awards</h3>
              </div>
              <div className="space-y-3">
                {resume.awards.map((award) => (
                  <div key={award.id} className="bg-white p-3 rounded-xl shadow-sm border border-green-200">
                    <h4 className="text-sm font-bold text-gray-900 leading-tight mb-1">{award.title}</h4>
                    <p className="text-xs text-gray-600 font-medium">{award.issuer}</p>
                    {award.dateReceived && <p className="text-xs text-gray-500 mt-1">{award.dateReceived}</p>}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
}
