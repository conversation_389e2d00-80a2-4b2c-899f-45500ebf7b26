import React from "react";
import SkillsDisplay from "@/components/resume/skills-display";
import { FullResume } from "@/db/schema";

interface GengarTemplateProps {
  resume: FullResume;
}

export default function GengarTemplate({ resume }: GengarTemplateProps) {
  const colorClasses = {
    blue: "text-blue-600 bg-blue-600 border-blue-600",
    green: "text-green-600 bg-green-600 border-green-600",
    purple: "text-purple-600 bg-purple-600 border-purple-600",
    red: "text-red-600 bg-red-600 border-red-600",
    orange: "text-orange-600 bg-orange-600 border-orange-600",
    teal: "text-teal-600 bg-teal-600 border-teal-600",
    pink: "text-pink-600 bg-pink-600 border-pink-600",
    indigo: "text-indigo-600 bg-indigo-600 border-indigo-600",
  };

  const selectedColor = colorClasses[resume.colorScheme as keyof typeof colorClasses] || colorClasses.blue;

  return (
    <div
      className={`max-w-5xl mx-auto bg-white shadow-lg font-${resume.fontFamily || "inter"} text-gray-900 print:shadow-none overflow-hidden`}
    >
      {/* Creative Header with Diagonal Design */}
      <header className="relative bg-gradient-to-br from-gray-900 to-gray-800 text-white px-8 py-12 overflow-hidden">
        <div
          className={`absolute top-0 right-0 w-64 h-64 ${selectedColor.split(" ")[1]} opacity-10 transform rotate-45 translate-x-32 -translate-y-32`}
        ></div>
        <div
          className={`absolute bottom-0 left-0 w-48 h-48 ${selectedColor.split(" ")[1]} opacity-10 transform rotate-45 -translate-x-24 translate-y-24`}
        ></div>

        <div className="relative z-10 flex items-center justify-between">
          <div className="flex-1">
            {/* Name - Bold and modern */}
            <h1 className="text-5xl font-black text-white mb-3 leading-tight tracking-tight">
              {resume.firstName}
              <span className={`${selectedColor.split(" ")[0].replace("text-", "text-")}`}> {resume.lastName}</span>
            </h1>

            {/* Job Title - Creative styling */}
            <h2 className="text-2xl font-medium text-gray-200 mb-6 leading-relaxed tracking-wide relative">
              <span className={`absolute -left-4 top-0 bottom-0 w-1 ${selectedColor.split(" ")[1]}`}></span>
              {resume.jobTitle}
            </h2>

            {/* Bio - Styled for readability */}
            {resume.bio && (
              <p className="text-lg text-gray-300 leading-relaxed max-w-3xl mb-6 font-light">{resume.bio}</p>
            )}

            {/* Contact in creative grid */}
            <div className="grid grid-cols-2 gap-4 text-sm font-medium">
              {resume.email && (
                <div className="flex items-center bg-black bg-opacity-20 px-4 py-2 rounded-lg">
                  <span className="mr-3 text-lg">✉</span>
                  {resume.email}
                </div>
              )}
              {resume.phone && (
                <div className="flex items-center bg-black bg-opacity-20 px-4 py-2 rounded-lg">
                  <span className="mr-3 text-lg">📞</span>
                  {resume.phone}
                </div>
              )}
              {resume.website && (
                <div className="flex items-center bg-black bg-opacity-20 px-4 py-2 rounded-lg">
                  <span className="mr-3 text-lg">🌐</span>
                  {resume.website}
                </div>
              )}
              {(resume.city || resume.country) && (
                <div className="flex items-center bg-black bg-opacity-20 px-4 py-2 rounded-lg">
                  <span className="mr-3 text-lg">📍</span>
                  {[resume.city, resume.country].filter(Boolean).join(", ")}
                </div>
              )}
            </div>
          </div>

          {/* Photo with creative border */}
          {resume.showPhoto && resume.photo && (
            <div className="ml-8 flex-shrink-0 relative">
              <div
                className={`absolute inset-0 ${selectedColor.split(" ")[1]} opacity-20 transform rotate-6 rounded-2xl`}
              ></div>
              <img
                src={resume.photo}
                alt={`${resume.firstName} ${resume.lastName}`}
                className="relative w-40 h-40 rounded-2xl object-cover border-4 border-white shadow-2xl transform -rotate-3 hover:rotate-0 transition-transform duration-300"
              />
            </div>
          )}
        </div>
      </header>

      {/* Asymmetric two-column layout */}
      <div className="flex">
        {/* Left Column - Main content */}
        <div className="w-3/5 px-8 py-8">
          {/* Experience Section */}
          {resume.experiences && resume.experiences.length > 0 && (
            <section className="mb-12">
              <div className="flex items-center mb-8">
                <div
                  className={`w-12 h-12 ${selectedColor.split(" ")[1]} rounded-xl flex items-center justify-center mr-4`}
                >
                  <span className="text-white text-xl font-bold">💼</span>
                </div>
                <h3 className="text-3xl font-bold text-gray-900 uppercase tracking-wide">Experience</h3>
              </div>

              <div className="space-y-10">
                {resume.experiences.map((experience, index) => (
                  <div key={experience.id} className="relative">
                    {/* Creative timeline */}
                    <div
                      className={`absolute -left-4 top-0 w-8 h-8 ${selectedColor.split(" ")[1]} rounded-full flex items-center justify-center z-10`}
                    >
                      <span className="text-white text-xs font-bold">{index + 1}</span>
                    </div>
                    <div
                      className={`absolute -left-2 top-8 bottom-0 w-px ${selectedColor.split(" ")[2]} opacity-30`}
                    ></div>

                    <div className="pl-8 bg-gradient-to-r from-gray-50 to-transparent p-6 rounded-r-2xl border-l-4 border-transparent hover:border-gray-300 transition-colors duration-300">
                      {/* Job Title - Bold and prominent */}
                      <h4 className="text-2xl font-bold text-gray-900 mb-3 leading-tight">{experience.title}</h4>

                      {/* Company and Date - Creative layout */}
                      <div className="flex items-center justify-between mb-4">
                        <h5 className={`text-xl font-semibold ${selectedColor.split(" ")[0]} leading-relaxed`}>
                          {experience.company}
                        </h5>
                        <div className="text-right">
                          <span className="text-base font-bold text-gray-700 bg-gray-200 px-3 py-1 rounded-full">
                            {experience.startDate} - {experience.isCurrent ? "Present" : experience.endDate}
                          </span>
                          {(experience.city || experience.country) && (
                            <p className="text-sm text-gray-600 mt-1">
                              📍 {[experience.city, experience.country].filter(Boolean).join(", ")}
                            </p>
                          )}
                        </div>
                      </div>

                      {/* Description */}
                      {experience.description && (
                        <div
                          className="text-base text-gray-700 leading-relaxed prose prose-base max-w-none prose-headings:font-semibold"
                          dangerouslySetInnerHTML={{ __html: experience.description }}
                        />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Projects Section */}
          {resume.projects && resume.projects.length > 0 && (
            <section className="mb-12">
              <div className="flex items-center mb-8">
                <div
                  className={`w-12 h-12 ${selectedColor.split(" ")[1]} rounded-xl flex items-center justify-center mr-4`}
                >
                  <span className="text-white text-xl font-bold">🚀</span>
                </div>
                <h3 className="text-3xl font-bold text-gray-900 uppercase tracking-wide">Projects</h3>
              </div>

              <div className="grid gap-8">
                {resume.projects.map((project) => (
                  <div
                    key={project.id}
                    className="bg-gradient-to-br from-white to-gray-50 p-6 rounded-2xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-300"
                  >
                    {/* Project Title */}
                    <h4 className="text-xl font-bold text-gray-900 mb-2 leading-tight">{project.title}</h4>

                    {/* Client and Date */}
                    <div className="flex items-center justify-between mb-4">
                      {project.client && (
                        <h5 className={`text-lg font-semibold ${selectedColor.split(" ")[0]} leading-relaxed`}>
                          {project.client}
                        </h5>
                      )}
                      <span className="text-sm font-bold text-gray-700 bg-gray-200 px-3 py-1 rounded-full">
                        {project.startDate} - {project.endDate}
                      </span>
                    </div>

                    {/* URL */}
                    {project.url && (
                      <p className="text-sm text-gray-600 mb-3">
                        <a
                          href={project.url}
                          className={`${selectedColor.split(" ")[0]} hover:underline font-medium flex items-center`}
                        >
                          🌐 {project.url}
                        </a>
                      </p>
                    )}

                    {/* Description */}
                    {project.description && (
                      <div
                        className="text-sm text-gray-700 leading-relaxed prose prose-sm max-w-none"
                        dangerouslySetInnerHTML={{ __html: project.description }}
                      />
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>

        {/* Right Column - Sidebar with creative sections */}
        <div className="w-2/5 bg-gradient-to-b from-gray-50 to-gray-100 px-6 py-8 border-l border-gray-200">
          {/* Education Section */}
          {resume.educations && resume.educations.length > 0 && (
            <section className="mb-10">
              <div className="flex items-center mb-6">
                <div
                  className={`w-10 h-10 ${selectedColor.split(" ")[1]} rounded-lg flex items-center justify-center mr-3`}
                >
                  <span className="text-white text-lg font-bold">🎓</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 uppercase tracking-wide">Education</h3>
              </div>

              <div className="space-y-6">
                {resume.educations.map((education) => (
                  <div key={education.id} className="bg-white p-5 rounded-xl shadow-sm border border-gray-200">
                    {/* Degree - Prominent */}
                    <h4 className="text-lg font-bold text-gray-900 mb-2 leading-tight">{education.degree}</h4>

                    {/* Field of Study */}
                    {education.fieldOfStudy && (
                      <p className="text-base font-semibold text-gray-700 mb-2">{education.fieldOfStudy}</p>
                    )}

                    {/* Institution */}
                    <h5 className={`text-base font-semibold ${selectedColor.split(" ")[0]} mb-3`}>
                      {education.institution}
                    </h5>

                    {/* Date and Location */}
                    <div className="text-sm text-gray-600 space-y-1">
                      <p className="font-medium">
                        {education.startDate} - {education.isCurrent ? "Present" : education.endDate}
                      </p>
                      {(education.city || education.country) && (
                        <p>📍 {[education.city, education.country].filter(Boolean).join(", ")}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Skills Section - Creative visualization */}
          {resume.skills && resume.skills.length > 0 && (
            <section className="mb-10">
              <div className="flex items-center mb-6">
                <div
                  className={`w-10 h-10 ${selectedColor.split(" ")[1]} rounded-lg flex items-center justify-center mr-3`}
                >
                  <span className="text-white text-lg font-bold">⚙️</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 uppercase tracking-wide">Skills</h3>
              </div>

              <SkillsDisplay
                skills={resume.skills}
                colorScheme={resume.colorScheme}
                variant="stars"
                groupByCategory={true}
                maxColumns={1}
                showProficiency={true}
              />
            </section>
          )}

          {/* Languages Section */}
          {resume.languages && resume.languages.length > 0 && (
            <section className="mb-10">
              <div className="flex items-center mb-6">
                <div
                  className={`w-10 h-10 ${selectedColor.split(" ")[1]} rounded-lg flex items-center justify-center mr-3`}
                >
                  <span className="text-white text-lg font-bold">🌍</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 uppercase tracking-wide">Languages</h3>
              </div>

              <div className="space-y-4">
                {resume.languages.map((language) => (
                  <div key={language.id} className="bg-white p-4 rounded-xl shadow-sm border border-gray-200">
                    <div className="flex justify-between items-center mb-3">
                      <span className="text-base font-semibold text-gray-800">{language.name}</span>
                      <span className="text-sm font-bold text-white bg-gray-800 px-3 py-1 rounded-full">
                        {language.proficiency}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className={`h-3 rounded-full ${selectedColor.split(" ")[1]} shadow-sm`}
                        style={{ width: `${language.proficiency}%` }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Additional sections in compact format */}
          {resume.certifications && resume.certifications.length > 0 && (
            <section className="mb-8">
              <div className="flex items-center mb-4">
                <div
                  className={`w-8 h-8 ${selectedColor.split(" ")[1]} rounded-lg flex items-center justify-center mr-3`}
                >
                  <span className="text-white text-sm font-bold">🏆</span>
                </div>
                <h3 className="text-lg font-bold text-gray-900 uppercase tracking-wide">Certifications</h3>
              </div>
              <div className="space-y-3">
                {resume.certifications.map((cert) => (
                  <div key={cert.id} className="bg-white p-3 rounded-lg shadow-sm border border-gray-200">
                    <h4 className="text-sm font-bold text-gray-900 leading-tight mb-1">{cert.title}</h4>
                    <p className="text-xs text-gray-600 font-medium">{cert.issuer}</p>
                    {cert.dateReceived && <p className="text-xs text-gray-500 mt-1">{cert.dateReceived}</p>}
                  </div>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
}
