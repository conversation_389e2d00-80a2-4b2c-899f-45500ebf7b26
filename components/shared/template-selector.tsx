"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>eader, <PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import Image from "next/image";
import React from "react";
import { Template } from "@/db/schema";

interface TemplateSelectorProps {
  templates: Template[];
  selectedTemplateId?: number;
  onTemplateSelect: (templateId: number) => void;
  className?: string;
  maxHeight?: string;
  gridCols?: number;
  showDetails?: boolean;
  showCategory?: boolean;
  showFeatures?: boolean;
  imageHeight?: string;
  isFreePlan?: boolean;
}

const TemplateSelector: React.FC<TemplateSelectorProps> = ({
  templates,
  selectedTemplateId,
  onTemplateSelect,
  className = "",
  isFreePlan = false,
}) => {
  const renderTemplateCard = (template: Template) => {
    const isSelected = selectedTemplateId === template.id;

    return (
      <Card
        key={template.id}
        isPressable
        className={`cursor-pointer transition-all duration-300 hover:shadow-xl group ${
          isSelected
            ? "ring-2 ring-purple-500 border-purple-300 shadow-lg transform scale-[1.02]"
            : "hover:border-gray-300 dark:hover:border-gray-600 hover:shadow-lg hover:-translate-y-1"
        }`}
        onPress={() => onTemplateSelect(template.id)}
      >
        <CardBody className="p-3">
          <div className="flex flex-col gap-3">
            {/* Template Thumbnail */}
            <div className="relative w-full min-h-44 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700">
              <Image
                width={640}
                height={905}
                alt={template.name}
                className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
                src={`/assets/images/templates/${template.slug}.jpg`}
              />

              {/* Overlay for better visual hierarchy */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              {/* Selection indicator */}
              {isSelected && (
                <div className="absolute top-2 right-2 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                  <Icon className="text-white w-4 h-4" icon="lucide:check" />
                </div>
              )}
            </div>

            {/* Template Info */}
            <div className="flex-1 min-w-0 space-y-2">
              <h3 className="font-semibold text-foreground truncate text-sm group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                {template.name}
              </h3>

              <div className="flex items-center justify-between">
                {template.category && (
                  <Chip
                    className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400"
                    size="sm"
                    variant="flat"
                  >
                    {template.category}
                  </Chip>
                )}
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    );
  };

  const content = <div className={`grid grid-cols-2 gap-4 ${className}`}>{templates.map(renderTemplateCard)}</div>;

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-3">
        <div className="w-6 h-6 rounded-md bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
          <Icon className="text-white w-4 h-4" icon="lucide:layout-template" />
        </div>
        <h3 className="font-semibold text-gray-900 dark:text-gray-100">Choose Template</h3>
      </div>
      {content}
    </div>
  );
};

export default TemplateSelector;
