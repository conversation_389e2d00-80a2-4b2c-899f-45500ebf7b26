import * as React from "react";

import { IconSvgProps } from "@/types";

export const Logo: React.FC<IconSvgProps> = ({ size = 36, width, height, ...props }) => (
  <svg fill="none" height={size || height} viewBox="0 0 32 32" width={size || width} {...props}>
    {/* Document background */}
    <rect
      x="6"
      y="4"
      width="16"
      height="20"
      rx="2"
      ry="2"
      fill="currentColor"
      fillOpacity="0.1"
      stroke="currentColor"
      strokeWidth="1.5"
    />

    {/* Document header/title bar */}
    <rect x="8" y="7" width="8" height="1.5" rx="0.75" fill="currentColor" fillOpacity="0.8" />

    {/* Document lines representing text */}
    <rect x="8" y="10" width="10" height="1" rx="0.5" fill="currentColor" fillOpacity="0.6" />
    <rect x="8" y="12.5" width="12" height="1" rx="0.5" fill="currentColor" fillOpacity="0.6" />
    <rect x="8" y="15" width="9" height="1" rx="0.5" fill="currentColor" fillOpacity="0.6" />
    <rect x="8" y="17.5" width="11" height="1" rx="0.5" fill="currentColor" fillOpacity="0.6" />

    {/* Speed/Lightning element - representing "Quick" */}
    <path d="M24 8L20 14h3l-4 8 6-10h-3l2-4z" fill="currentColor" fillOpacity="0.9" />

    {/* Decorative corner fold */}
    <path d="M19 4v3h3L19 4z" fill="currentColor" fillOpacity="0.3" />
  </svg>
);
