import React from "react";
import { Template } from "@/db/schema";

interface TemplateSelectorProps {
  templates: Template[];
  selectedTemplateId?: number;
  onTemplateSelect: (templateId: number) => void;
  className?: string;
  isFreePlan?: boolean;
}

export const TemplateSelector: React.FC<TemplateSelectorProps> = ({
  templates,
  selectedTemplateId,
  onTemplateSelect,
  className = "",
  isFreePlan,
}) => {
  return (
    <div className={`template-selector ${className}`}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {templates.map((template: Template) => {
          const isSelected = selectedTemplateId === template.id;
          const isPremiumTemplate = template.isPremium;
          const isDisabled = isFreePlan && isPremiumTemplate;

          return (
            <button
              key={template.id}
              type="button"
              disabled={isDisabled}
              className={`template-option p-4 border rounded-lg transition-all relative ${
                isDisabled ? "opacity-60 cursor-not-allowed border-gray-200" : "hover:shadow-md cursor-pointer"
              } ${isSelected ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300"}`}
              onClick={() => !isDisabled && onTemplateSelect(template.id)}
            >
              {isPremiumTemplate && (
                <div className="absolute top-2 right-2 bg-gradient-to-r from-yellow-400 to-orange-400 text-white text-xs font-bold px-2 py-1 rounded shadow-sm">
                  {isFreePlan ? "🔒 Premium" : "Premium"}
                </div>
              )}
              <h3 className={`font-semibold mb-2 ${isDisabled ? "text-gray-500" : "text-gray-900"}`}>
                {template.name}
              </h3>
              {template.category && (
                <span
                  className={`inline-block px-2 py-1 text-xs rounded ${
                    isDisabled ? "bg-gray-100 text-gray-500" : "bg-gray-100 text-gray-700"
                  }`}
                >
                  {template.category}
                </span>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
};
