"use client";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { useRouter } from "next/navigation";
import { trpc } from "@/app/_trpc/client";

interface CreateResumeModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function CreateResumeModal({ isOpen, onClose }: CreateResumeModalProps) {
  const router = useRouter();
  const [resumeTitle, setResumeTitle] = useState("");
  const [isCreating, setIsCreating] = useState(false);

  const createResumeMutation = trpc.resumes.createResume.useMutation();

  const handleClose = () => {
    onClose();
    setResumeTitle("");
  };

  const handleCreateResume = async () => {
    if (!resumeTitle.trim()) {
      toast.error("Please enter a resume title");
      return;
    }

    setIsCreating(true);
    try {
      const result = await createResumeMutation.mutateAsync({
        title: resumeTitle.trim(),
      });

      if (result.success && result.resumeId) {
        toast.success("Resume created successfully!");
        router.push(`/resumes/edit/${result.resumeId}`);
        handleClose();
      } else {
        toast.error("Failed to create resume");
      }
    } catch (error) {
      toast.error("An error occurred while creating the resume");
      console.error("Error creating resume:", error);
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="md">
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h3 className="text-lg font-semibold">Create New Resume</h3>
          <p className="text-sm text-gray-500">Give your resume a title to get started</p>
        </ModalHeader>
        <ModalBody>
          <Input
            label="Resume Title"
            placeholder="e.g., Software Engineer Resume"
            value={resumeTitle}
            onChange={(e) => setResumeTitle(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === "Enter" && !isCreating) {
                handleCreateResume();
              }
            }}
            autoFocus
            isRequired
            variant="bordered"
            startContent={<Icon icon="lucide:file-text" className="w-4 h-4 text-gray-400" />}
          />
        </ModalBody>
        <ModalFooter>
          <Button variant="flat" onPress={handleClose} isDisabled={isCreating}>
            Cancel
          </Button>
          <Button
            color="primary"
            onPress={handleCreateResume}
            isLoading={isCreating}
            isDisabled={!resumeTitle.trim() || isCreating}
          >
            Create Resume
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
}