import Image from "next/image";
import React from "react";
import {
  EducationItem,
  ExperienceItem,
  formatDate,
  formatDateRange,
  formatLocation,
  getA4PageClasses,
  getFullName,
  getMainContentClasses,
  getPhotoClasses,
  getPrintSafeClasses,
  getSectionSpacing,
  getSectionTranslations,
  getSidebarClasses,
  getTextClasses,
  getTitleClasses,
  getTwoColumnClasses,
  LanguageItem,
  ProfessionalSummary,
  ProjectItem,
  ResumeSection,
  SkillsSection,
  SocialProfile,
  TemplateProps,
  useTemplateLocale,
} from "./shared";

// Custom sidebar section for chikorita template
const ChikoritaSidebarSection: React.FC<{
  title: string;
  children: React.ReactNode;
}> = ({ title, children }) => (
  <section className="mb-3 md:mb-4 sidebar-section">
    <h3 className={`${getTitleClasses(3)} mb-2 pb-1 border-b border-white text-white`}>{title}</h3>
    {children}
  </section>
);

// Custom contact component with green icons
const ChikoritaContact: React.FC<{
  icon: string;
  children: React.ReactNode;
}> = ({ icon, children }) => (
  <div className="flex items-start">
    <span className="text-green-600 me-2 flex-shrink-0">{icon}</span>
    {children}
  </div>
);

/**
 * Chikorita Resume Template - Two-Column Layout with Green Sidebar
 * - Photo and contact info header
 * - Left column for main sections (Profiles, Summary, Experience, Education, Projects)
 * - Right green sidebar for Skills, Certifications, Languages, References
 * - Clean typography matching the reference design
 * - ATS-friendly structure with proper hierarchy
 */
const ChikoritaTemplate: React.FC<TemplateProps> = ({ resume, className = "" }) => {
  const locale = useTemplateLocale();
  const sectionTitles = getSectionTranslations(locale);
  const fullName = getFullName(resume.firstName, resume.lastName, locale);
  const location = formatLocation(resume.city, resume.country);

  return (
    <div className={getPrintSafeClasses(`chikorita-template text-gray-900 font-sans w-full ${className}`)}>
      {/* Page wrapper with A4 proportions */}
      <div className={getA4PageClasses()}>
        {/* Two-Column Layout - Full Height */}
        <div className={`${getTwoColumnClasses()} h-full min-h-[297mm]`}>
          {/* Left Column - Main Content */}
          <main className={`${getMainContentClasses()} bg-white flex flex-col min-h-[297mm]`}>
            {/* Header with Photo and Contact Info */}
            <header className="p-3 md:p-4 pb-2 md:pb-3 resume-header flex-shrink-0">
              <div className="flex flex-col md:flex-row items-center md:items-start gap-4 md:gap-6">
                {/* Photo */}
                {resume.showPhoto && resume.photo ? (
                  <div className="flex-shrink-0">
                    <Image
                      alt={fullName}
                      className={`${getPhotoClasses()} border border-gray-300`}
                      height={128}
                      src={resume.photo}
                      width={96}
                    />
                  </div>
                ) : null}

                {/* Name, Title and Contact Info */}
                <div className="flex-1 text-center md:text-left">
                  <h1 className={`${getTitleClasses(1)} text-gray-900 mb-1`}>{fullName}</h1>
                  <p className={`${getTextClasses("lg")} text-gray-700 mb-3 md:mb-4`}>{resume.jobTitle}</p>

                  {/* Contact Information with green icons */}
                  <div className={`space-y-1 ${getTextClasses("sm")} text-gray-700 contact-section`}>
                    {location && (
                      <ChikoritaContact icon="📍">
                        <span>{location}</span>
                      </ChikoritaContact>
                    )}
                    {resume.phone && (
                      <ChikoritaContact icon="📞">
                        <span>{resume.phone}</span>
                      </ChikoritaContact>
                    )}
                    {resume.email && (
                      <ChikoritaContact icon="✉️">
                        <span className="break-all">{resume.email}</span>
                      </ChikoritaContact>
                    )}
                    {resume.website && (
                      <ChikoritaContact icon="🔗">
                        <span className="break-all">{resume.website}</span>
                      </ChikoritaContact>
                    )}
                  </div>
                </div>
              </div>
            </header>

            {/* Main Content Sections */}
            <div className="px-3 md:px-4 flex-1 flex flex-col justify-start">
              {/* Profiles */}
              {resume.profiles && resume.profiles.length > 0 && (
                <ResumeSection title={sectionTitles.profiles} variant="default">
                  <SocialProfile
                    className="grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 md:gap-4"
                    layout="grid"
                    profiles={resume.profiles}
                    showNetworkLabel={true}
                  />
                </ResumeSection>
              )}

              {/* Summary */}
              {resume.bio && (
                <ResumeSection title={sectionTitles.summary} variant="default">
                  <ProfessionalSummary bio={resume.bio} variant="paragraph" />
                </ResumeSection>
              )}

              {/* Experience */}
              {resume.experiences && resume.experiences.length > 0 && (
                <ResumeSection title={sectionTitles.experience} variant="default">
                  <div className="space-y-3 md:space-y-4">
                    {resume.experiences.map((exp) => (
                      <ExperienceItem
                        key={exp.id}
                        className="experience-item"
                        experience={exp}
                        locale={locale}
                        showWebsiteIcon={true}
                        variant="standard"
                      />
                    ))}
                  </div>
                </ResumeSection>
              )}

              {/* Education */}
              {resume.educations && resume.educations.length > 0 && (
                <ResumeSection title={sectionTitles.education} variant="default">
                  <div className="space-y-3 md:space-y-4">
                    {resume.educations.map((edu) => (
                      <EducationItem
                        key={edu.id}
                        className="education-item"
                        education={edu}
                        locale={locale}
                        variant="standard"
                      />
                    ))}
                  </div>
                </ResumeSection>
              )}

              {/* Projects */}
              {resume.projects && resume.projects.length > 0 && (
                <ResumeSection title={sectionTitles.projects} variant="default">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
                    {resume.projects.map((project) => (
                      <ProjectItem
                        key={project.id}
                        className="project-item"
                        project={project}
                        showClient={true}
                        showTechnologies={false}
                        variant="standard"
                      />
                    ))}
                  </div>
                </ResumeSection>
              )}
            </div>
          </main>

          {/* Right Column - Green Sidebar */}
          <aside className={`${getSidebarClasses("bg-green-600")} text-white p-3 md:p-4 h-full min-h-[297mm]`}>
            {/* Skills */}
            {resume.skills && resume.skills.length > 0 && (
              <ChikoritaSidebarSection title={sectionTitles.skills}>
                <div className="text-white">
                  <SkillsSection
                    className={`[&_h4]:text-white [&_h4]:font-bold [&_p]:text-white [&_p]:${getTextClasses("sm")}`}
                    layout="list"
                    skills={resume.skills}
                  />
                </div>
              </ChikoritaSidebarSection>
            )}

            {/* Certifications */}
            {resume.certifications && resume.certifications.length > 0 && (
              <ChikoritaSidebarSection title={sectionTitles.certifications}>
                <div className="space-y-2 md:space-y-3">
                  {resume.certifications.map((cert) => (
                    <div key={cert.id} className="text-white certification-item">
                      <h4 className={`font-bold text-white ${getTextClasses("sm")}`}>{cert.title}</h4>
                      <p className={`text-white ${getTextClasses("sm")}`}>{cert.issuer}</p>
                      {cert.dateReceived && <p className={`text-white ${getTextClasses("sm")}`}>{cert.dateReceived}</p>}
                    </div>
                  ))}
                </div>
              </ChikoritaSidebarSection>
            )}

            {/* Languages */}
            {resume.languages && resume.languages.length > 0 && (
              <ChikoritaSidebarSection title={sectionTitles.languages}>
                <div className="space-y-2 md:space-y-3">
                  {resume.languages.map((lang) => (
                    <div key={lang.id} className="text-white">
                      <LanguageItem
                        className="[&_span]:text-white [&_span]:text-sm"
                        language={lang}
                        showBars={false}
                        showLevel={true}
                      />
                    </div>
                  ))}
                </div>
              </ChikoritaSidebarSection>
            )}

            {/* Awards */}
            {resume.awards && resume.awards.length > 0 && (
              <ChikoritaSidebarSection title={sectionTitles.awards}>
                <div className="space-y-2 md:space-y-3">
                  {resume.awards.map((award) => (
                    <div key={award.id} className="text-white award-item">
                      <h4 className={`font-bold text-white ${getTextClasses("sm")}`}>{award.title}</h4>
                      <p className={`text-white ${getTextClasses("sm")}`}>{award.issuer}</p>
                      {award.dateReceived && (
                        <p className={`text-white ${getTextClasses("sm")}`}>
                          {formatDate(award.dateReceived, false, locale)}
                        </p>
                      )}
                      {award.description && (
                        <div
                          dangerouslySetInnerHTML={{ __html: award.description }}
                          className={`text-white ${getTextClasses("sm")} mt-1`}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </ChikoritaSidebarSection>
            )}

            {/* Volunteering */}
            {resume.volunteerings && resume.volunteerings.length > 0 && (
              <ChikoritaSidebarSection title={sectionTitles.volunteering}>
                <div className="space-y-2 md:space-y-3">
                  {resume.volunteerings.map((volunteering) => (
                    <div key={volunteering.id} className="text-white volunteering-item">
                      <h4 className={`font-bold text-white ${getTextClasses("sm")}`}>{volunteering.role}</h4>
                      <p className={`text-white ${getTextClasses("sm")}`}>{volunteering.organization}</p>
                      {(volunteering.startDate || volunteering.endDate) && (
                        <p className={`text-white ${getTextClasses("sm")}`}>
                          {formatDateRange(volunteering.startDate, volunteering.endDate, 0, locale)}
                        </p>
                      )}
                      {volunteering.description && (
                        <div
                          dangerouslySetInnerHTML={{
                            __html: volunteering.description,
                          }}
                          className={`text-white ${getTextClasses("sm")} mt-1`}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </ChikoritaSidebarSection>
            )}

            {/* Hobbies */}
            {resume.hobbies && resume.hobbies.length > 0 && (
              <ChikoritaSidebarSection title={sectionTitles.hobbies}>
                <p className={`text-white ${getTextClasses("sm")} leading-relaxed`}>
                  {resume.hobbies.map((hobby) => hobby.name).join(", ")}
                </p>
              </ChikoritaSidebarSection>
            )}

            {/* References */}
            {resume.references && resume.references.length > 0 ? (
              <ChikoritaSidebarSection title={sectionTitles.references}>
                <div className="space-y-2 md:space-y-3">
                  {resume.references.map((reference) => (
                    <div key={reference.id} className="text-white reference-item">
                      <h4 className={`font-bold text-white ${getTextClasses("sm")}`}>{reference.name}</h4>
                      <p className={`text-white ${getTextClasses("sm")}`}>
                        {reference.position} at {reference.company}
                      </p>
                      <div className={`text-white ${getTextClasses("sm")} mt-1`}>
                        {reference.email && <p className="break-all">{reference.email}</p>}
                        {reference.phone && <p>{reference.phone}</p>}
                      </div>
                      {reference.description && (
                        <div
                          dangerouslySetInnerHTML={{
                            __html: reference.description,
                          }}
                          className={`text-white ${getTextClasses("sm")} mt-1`}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </ChikoritaSidebarSection>
            ) : (
              <ChikoritaSidebarSection title={sectionTitles.references}>
                <p className={`text-white ${getTextClasses("sm")}`}>Available upon request</p>
              </ChikoritaSidebarSection>
            )}
          </aside>
        </div>
      </div>
    </div>
  );
};

export default ChikoritaTemplate;
