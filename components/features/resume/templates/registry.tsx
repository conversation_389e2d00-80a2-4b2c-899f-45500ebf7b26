"use client";
import React, { useMemo } from "react";
import { trpc } from "@/app/_trpc/client";
import { TemplateRegistry, TemplateRenderer } from "@/components/shared/template-system";
import { ResumeTemplateProps } from "@/components/shared/template-system/types";
import { FullResume } from "@/db/schema";
// Template imports
import {
  AzurillTemplate,
  BronzorTemplate,
  ChikoritaTemplate,
  DittoTemplate,
  GengarTemplate,
  GlalieTemplate,
  KakunaTemplate,
  LeafishTemplate,
  NosepassTemplate,
  OnyxTemplate,
  PikachuTemplate,
  RhyhornTemplate,
} from "./";

// Create resume template registry
const resumeTemplateRegistry = new TemplateRegistry<ResumeTemplateProps>();

// Register all templates
resumeTemplateRegistry.register({
  id: "azurill",
  slug: "azurill",
  name: "Azurill",
  component: AzurillTemplate,
  category: "modern",
});

resumeTemplateRegistry.register({
  id: "bronzor",
  slug: "bronzor",
  name: "<PERSON>ron<PERSON>",
  component: BronzorTemplate,
  category: "professional",
});

resumeTemplateRegistry.register({
  id: "chikorita",
  slug: "chikorita",
  name: "Chikorita",
  component: ChikoritaTemplate,
  category: "creative",
});

resumeTemplateRegistry.register({
  id: "ditto",
  slug: "ditto",
  name: "Ditto",
  component: DittoTemplate,
  category: "modern",
});

resumeTemplateRegistry.register({
  id: "gengar",
  slug: "gengar",
  name: "Gengar",
  component: GengarTemplate,
  category: "creative",
});

resumeTemplateRegistry.register({
  id: "glalie",
  slug: "glalie",
  name: "Glalie",
  component: GlalieTemplate,
  category: "professional",
});

resumeTemplateRegistry.register({
  id: "kakuna",
  slug: "kakuna",
  name: "Kakuna",
  component: KakunaTemplate,
  category: "modern",
});

resumeTemplateRegistry.register({
  id: "leafish",
  slug: "leafish",
  name: "Leafish",
  component: LeafishTemplate,
  category: "creative",
});

resumeTemplateRegistry.register({
  id: "nosepass",
  slug: "nosepass",
  name: "Nosepass",
  component: NosepassTemplate,
  category: "professional",
});

resumeTemplateRegistry.register({
  id: "onyx",
  slug: "onyx",
  name: "Onyx",
  component: OnyxTemplate,
  category: "modern",
});

resumeTemplateRegistry.register({
  id: "pikachu",
  slug: "pikachu",
  name: "Pikachu",
  component: PikachuTemplate,
  category: "creative",
});

resumeTemplateRegistry.register({
  id: "rhyhorn",
  slug: "rhyhorn",
  name: "Rhyhorn",
  component: RhyhornTemplate,
  category: "professional",
});

interface ModernTemplateRendererProps {
  resume: FullResume;
  className?: string;
}

// Enhanced Template renderer component
export const ModernTemplateRenderer = React.forwardRef<HTMLDivElement, ModernTemplateRendererProps>(
  ({ resume, className }, ref) => {
    const getTemplates = trpc.templates.getTemplates.useQuery({});
    const { data: templates = [] } = getTemplates;

    const template = useMemo(() => {
      return templates.find((template) => template.id === resume.templateId) || templates[0];
    }, [resume.templateId, templates]);

    if (!template) {
      return <div className="error">No templates available</div>;
    }

    return (
      <div ref={ref}>
        <TemplateRenderer
          registry={resumeTemplateRegistry}
          templateSlug={template.slug}
          templateProps={{ resume, className }}
          fallbackComponent={BronzorTemplate}
        />
      </div>
    );
  },
);

ModernTemplateRenderer.displayName = "ModernTemplateRenderer";

export { resumeTemplateRegistry };
export default ModernTemplateRenderer;
