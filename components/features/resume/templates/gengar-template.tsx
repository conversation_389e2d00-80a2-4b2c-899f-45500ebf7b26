import Image from "next/image";
import React from "react";
import {
  CertificationItem,
  EducationItem,
  ExperienceItem,
  formatDate,
  formatDateRange,
  formatLocation,
  getA4PageClasses,
  getFullName,
  getGridClasses,
  getMainContentClasses,
  getPhotoClasses,
  getPrintSafeClasses,
  getSectionSpacing,
  getSectionTranslations,
  getSidebarClasses,
  getTextClasses,
  getTitleClasses,
  getTwoColumnClasses,
  LanguageItem,
  ProfessionalSummary,
  ProjectItem,
  ReferenceItem,
  SkillsSection,
  SocialProfile,
  TemplateProps,
  useTemplateLocale,
} from "./shared";

// Custom main content section for gengar template
const GengarSection: React.FC<{
  title: string;
  children: React.ReactNode;
}> = ({ title, children }) => (
  <section className={getSectionSpacing()}>
    <h2 className={`${getTitleClasses(2)} mb-3 text-gray-900 border-b border-gray-400 pb-1`}>{title}</h2>
    {children}
  </section>
);

// Custom sidebar section for gengar template
const GengarSidebarSection: React.FC<{
  title: string;
  children: React.ReactNode;
}> = ({ title, children }) => (
  <section className="sidebar-section mb-4 md:mb-6">
    <h3 className={`${getTitleClasses(3)} mb-2 md:mb-3 text-white border-b border-teal-400 pb-1`}>{title}</h3>
    {children}
  </section>
);

/**
 * Gengar Resume Template - Clean Two-Column Layout
 * - Teal sidebar on left with photo, contact, and secondary info
 * - White main content area on right for primary sections
 * - Clean typography and professional appearance
 * - Professional color scheme matching reference design
 * - ATS-friendly structure with proper hierarchy
 */
const GengarTemplate: React.FC<TemplateProps> = ({ resume, className = "" }) => {
  const locale = useTemplateLocale();
  const sectionTitles = getSectionTranslations(locale);
  const fullName = getFullName(resume.firstName, resume.lastName, locale);
  const location = formatLocation(resume.city, resume.country);

  return (
    <div className={getPrintSafeClasses(`gengar-template bg-white text-gray-900 font-sans ${className}`)}>
      {/* Page wrapper with A4 proportions */}
      <div className={getA4PageClasses("bg-white")}>
        {/* Two-Column Layout - Full Height */}
        <div className={`${getTwoColumnClasses()} min-h-[297mm]`}>
          {/* Left Sidebar - Teal Background */}
          <aside className={`${getSidebarClasses("bg-teal-500")} text-white p-4 md:p-6`}>
            {/* Photo */}
            {resume.showPhoto && resume.photo ? (
              <div className="mb-4 md:mb-6 text-center">
                <Image
                  alt={fullName}
                  className={`${getPhotoClasses()} mx-auto`}
                  height={160}
                  src={resume.photo}
                  width={128}
                />
              </div>
            ) : null}

            {/* Name and Title */}
            <div className="text-center mb-4 md:mb-6">
              <h1 className={`${getTitleClasses(2)} text-white mb-2`}>{fullName}</h1>
              <p className={`${getTextClasses("sm")} text-teal-100`}>{resume.jobTitle}</p>
            </div>

            {/* Contact Information */}
            <div className="contact-section mb-4 md:mb-6">
              <div className={`space-y-2 md:space-y-3 ${getTextClasses("sm")} text-white`}>
                {location && (
                  <div className="flex items-start">
                    <span className="text-teal-200 me-2 mt-0.5 flex-shrink-0">📍</span>
                    <span>{location}</span>
                  </div>
                )}
                {resume.phone && (
                  <div className="flex items-start">
                    <span className="text-teal-200 me-2 mt-0.5 flex-shrink-0">📞</span>
                    <span>{resume.phone}</span>
                  </div>
                )}
                {resume.email && (
                  <div className="flex items-start">
                    <span className="text-teal-200 me-2 mt-0.5 flex-shrink-0">✉️</span>
                    <span className="break-all">{resume.email}</span>
                  </div>
                )}
                {resume.website && (
                  <div className="flex items-start">
                    <span className="text-teal-200 me-2 mt-0.5 flex-shrink-0">🔗</span>
                    <span className="break-all">{resume.website}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Profiles */}
            {resume.profiles && resume.profiles.length > 0 && (
              <GengarSidebarSection title={sectionTitles.profiles}>
                <SocialProfile
                  className={`[&_span]:text-white [&_div]:text-white [&_div]:${getTextClasses("sm")}`}
                  layout="vertical"
                  profiles={resume.profiles}
                  showNetworkLabel={true}
                />
              </GengarSidebarSection>
            )}

            {/* Skills */}
            {resume.skills && resume.skills.length > 0 && (
              <GengarSidebarSection title={sectionTitles.skills}>
                <SkillsSection
                  className={`[&_h4]:text-white [&_h4]:font-bold [&_h4]:${getTextClasses("sm")} [&_p]:text-teal-100 [&_p]:${getTextClasses("sm")}`}
                  layout="list"
                  skills={resume.skills}
                />
              </GengarSidebarSection>
            )}

            {/* Languages */}
            {resume.languages && resume.languages.length > 0 && (
              <GengarSidebarSection title={sectionTitles.languages}>
                <div className="space-y-2 md:space-y-3">
                  {resume.languages.map((language) => (
                    <LanguageItem
                      key={language.id}
                      className={`[&_h4]:text-white [&_h4]:${getTextClasses("sm")} [&_p]:text-teal-100 [&_p]:${getTextClasses("sm")}`}
                      language={language}
                      showBars={false}
                      showLevel={true}
                    />
                  ))}
                </div>
              </GengarSidebarSection>
            )}

            {/* Certifications */}
            {resume.certifications && resume.certifications.length > 0 && (
              <GengarSidebarSection title={sectionTitles.certifications}>
                <div className="space-y-2 md:space-y-3">
                  {resume.certifications.map((cert) => (
                    <CertificationItem
                      key={cert.id}
                      certification={cert}
                      className={`certification-item [&_h3]:text-white [&_h3]:${getTextClasses("sm")} [&_p]:text-teal-100 [&_p]:${getTextClasses("sm")}`}
                      showCredentialId={false}
                    />
                  ))}
                </div>
              </GengarSidebarSection>
            )}

            {/* Hobbies */}
            {resume.hobbies && resume.hobbies.length > 0 && (
              <GengarSidebarSection title={sectionTitles.hobbies}>
                <p className={`text-teal-100 ${getTextClasses("sm")} leading-relaxed`}>
                  {resume.hobbies.map((hobby) => hobby.name).join(", ")}
                </p>
              </GengarSidebarSection>
            )}
          </aside>

          {/* Right Column - Main Content */}
          <main className={`${getMainContentClasses()} p-4 md:p-6`}>
            {/* Summary */}
            {resume.bio && (
              <GengarSection title={sectionTitles.summary}>
                <ProfessionalSummary bio={resume.bio} variant="paragraph" />
              </GengarSection>
            )}

            {/* Experience */}
            {resume.experiences && resume.experiences.length > 0 && (
              <GengarSection title={sectionTitles.experience}>
                <div className="space-y-4 md:space-y-6">
                  {resume.experiences.map((exp) => (
                    <ExperienceItem
                      key={exp.id}
                      className="experience-item"
                      experience={exp}
                      locale={locale}
                      showWebsiteIcon={true}
                      variant="standard"
                    />
                  ))}
                </div>
              </GengarSection>
            )}

            {/* Education */}
            {resume.educations && resume.educations.length > 0 && (
              <GengarSection title={sectionTitles.education}>
                <div className="space-y-3 md:space-y-4">
                  {resume.educations.map((edu) => (
                    <EducationItem
                      key={edu.id}
                      className="education-item"
                      education={edu}
                      locale={locale}
                      variant="standard"
                    />
                  ))}
                </div>
              </GengarSection>
            )}

            {/* Projects */}
            {resume.projects && resume.projects.length > 0 && (
              <GengarSection title={sectionTitles.projects}>
                <div className="space-y-3 md:space-y-4">
                  {resume.projects.map((project) => (
                    <ProjectItem
                      key={project.id}
                      className="project-item"
                      project={project}
                      showClient={true}
                      showTechnologies={false}
                      variant="standard"
                    />
                  ))}
                </div>
              </GengarSection>
            )}

            {/* Awards */}
            {resume.awards && resume.awards.length > 0 && (
              <GengarSection title={sectionTitles.awards}>
                <div className="space-y-2 md:space-y-3">
                  {resume.awards.map((award) => (
                    <div
                      key={award.id}
                      className="award-item flex flex-col md:flex-row md:justify-between md:items-start"
                    >
                      <div className="flex-1">
                        <h4 className={`font-bold text-gray-900 ${getTextClasses("sm")}`}>{award.title}</h4>
                        <p className={`text-gray-700 ${getTextClasses("sm")}`}>{award.issuer}</p>
                        {award.description && (
                          <div
                            dangerouslySetInnerHTML={{ __html: award.description }}
                            className={`text-gray-700 ${getTextClasses("sm")} mt-1`}
                          />
                        )}
                      </div>
                      {award.dateReceived && (
                        <span className={`text-gray-600 ${getTextClasses("sm")} mt-1 md:mt-0`}>
                          {formatDate(award.dateReceived, false, locale)}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              </GengarSection>
            )}

            {/* Volunteering */}
            {resume.volunteerings && resume.volunteerings.length > 0 && (
              <GengarSection title={sectionTitles.volunteering}>
                <div className="space-y-3 md:space-y-4">
                  {resume.volunteerings.map((volunteering) => (
                    <div key={volunteering.id} className="volunteering-item">
                      <div className="flex flex-col md:flex-row md:justify-between md:items-start mb-1">
                        <div className="flex-1">
                          <h4 className="font-bold text-gray-900">{volunteering.role}</h4>
                          <p className={`text-gray-700 ${getTextClasses("sm")}`}>{volunteering.organization}</p>
                        </div>
                        {(volunteering.startDate || volunteering.endDate) && (
                          <span className={`text-gray-600 ${getTextClasses("sm")} mt-1 md:mt-0`}>
                            {formatDateRange(volunteering.startDate, volunteering.endDate, 0, locale)}
                          </span>
                        )}
                      </div>
                      {volunteering.description && (
                        <div
                          dangerouslySetInnerHTML={{
                            __html: volunteering.description,
                          }}
                          className={`text-gray-700 ${getTextClasses("sm")} mt-2`}
                        />
                      )}
                    </div>
                  ))}
                </div>
              </GengarSection>
            )}

            {/* References */}
            {resume.references && resume.references.length > 0 ? (
              <GengarSection title={sectionTitles.references}>
                <div className={`${getGridClasses(2)} gap-3 md:gap-4`}>
                  {resume.references.map((reference) => (
                    <ReferenceItem
                      key={reference.id}
                      reference={reference}
                      showIcons={true}
                      textSize="sm"
                    />
                  ))}
                </div>
              </GengarSection>
            ) : (
              <GengarSection title={sectionTitles.references}>
                <p className={`text-gray-700 ${getTextClasses("sm")}`}>Available upon request</p>
              </GengarSection>
            )}
          </main>
        </div>
      </div>
    </div>
  );
};

export default GengarTemplate;
