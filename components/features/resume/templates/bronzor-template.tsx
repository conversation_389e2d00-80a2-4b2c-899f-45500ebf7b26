import React from "react";
import {
  CertificationItem,
  EducationItem,
  ExperienceItem,
  formatDate,
  formatDateRange,
  formatLocation,
  getA4PageClasses,
  getContactClasses,
  getGridClasses,
  getHeaderClasses,
  getPrintSafeClasses,
  getSectionSpacing,
  getSectionTranslations,
  getTextClasses,
  getTitleClasses,
  LanguageItem,
  ProfessionalSummary,
  ProjectItem,
  ReferenceItem,
  ResumeHeader,
  ResumeSection,
  SkillsSection,
  SocialProfile,
  TemplateProps,
  useTemplateLocale,
} from "./shared";

// Custom contact component for this template's green dot style
const BronzorContact: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <div className="flex items-center flex-wrap">
    <span className="w-2 h-2 bg-green-500 rounded-full me-2 flex-shrink-0" />
    {children}
  </div>
);

/**
 * Bronzor Resume Template - Clean Professional Style
 * - Minimal, clean design with horizontal section dividers
 * - Single column layout with excellent readability
 * - Professional typography and spacing
 * - ATS-friendly structure
 */
const BronzorTemplate: React.FC<TemplateProps> = ({ resume, className = "" }) => {
  const locale = useTemplateLocale();
  const sectionTitles = getSectionTranslations(locale);
  const location = formatLocation(resume.city, resume.country);

  return (
    <div className={getPrintSafeClasses(`bronzor-template bg-white text-gray-900 font-sans ${className}`)}>
      {/* Page wrapper with A4 proportions */}
      <main className={getA4PageClasses("p-4 md:p-6 lg:p-8")}>
        {/* Header with custom contact styling */}
        <ResumeHeader
          className={`${getHeaderClasses()} mb-6 md:mb-8`}
          firstName={resume.firstName}
          jobTitle={resume.jobTitle}
          lastName={resume.lastName}
          layout="centered"
          locale={locale}
          photo={resume.photo}
          showPhoto={resume.showPhoto === 1}
        >
          {/* Contact info with green bullets */}
          <div className={`${getContactClasses()} ${getTextClasses("sm")} text-gray-600`}>
            {location && (
              <BronzorContact>
                <span>{location}</span>
              </BronzorContact>
            )}
            {resume.phone && (
              <BronzorContact>
                <span>{resume.phone}</span>
              </BronzorContact>
            )}
            {resume.email && (
              <BronzorContact>
                <span className="break-all">{resume.email}</span>
              </BronzorContact>
            )}
            {resume.website && (
              <BronzorContact>
                <span className="break-all">{resume.website}</span>
              </BronzorContact>
            )}
          </div>
        </ResumeHeader>

        {/* Profiles Section */}
        {resume.profiles && resume.profiles.length > 0 && (
          <ResumeSection title={sectionTitles.profiles} variant="default">
            <SocialProfile
              className={getGridClasses(3) + " gap-3 md:gap-4"}
              layout="grid"
              profiles={resume.profiles}
              showNetworkLabel={true}
            />
          </ResumeSection>
        )}

        {/* Summary Section */}
        {resume.bio && (
          <ResumeSection title={sectionTitles.summary} variant="default">
            <ProfessionalSummary bio={resume.bio} variant="paragraph" />
          </ResumeSection>
        )}

        {/* Experience Section */}
        {resume.experiences && resume.experiences.length > 0 && (
          <ResumeSection title={sectionTitles.experience} variant="default">
            <div className="space-y-4 md:space-y-6">
              {resume.experiences.map((exp) => (
                <ExperienceItem
                  key={exp.id}
                  className="experience-item"
                  experience={exp}
                  locale={locale}
                  showWebsiteIcon={true}
                  variant="standard"
                />
              ))}
            </div>
          </ResumeSection>
        )}

        {/* Education Section */}
        {resume.educations && resume.educations.length > 0 && (
          <ResumeSection title={sectionTitles.education} variant="default">
            <div className="space-y-3 md:space-y-4">
              {resume.educations.map((edu) => (
                <EducationItem
                  key={edu.id}
                  className="education-item"
                  education={edu}
                  locale={locale}
                  variant="standard"
                />
              ))}
            </div>
          </ResumeSection>
        )}

        {/* Projects Section */}
        {resume.projects && resume.projects.length > 0 && (
          <ResumeSection title={sectionTitles.projects} variant="default">
            <div className={getGridClasses(2) + " gap-4 md:gap-6"}>
              {resume.projects.map((project) => (
                <ProjectItem
                  key={project.id}
                  className="project-item"
                  project={project}
                  showClient={true}
                  showTechnologies={false}
                  variant="standard"
                />
              ))}
            </div>
          </ResumeSection>
        )}

        {/* Skills Section */}
        {resume.skills && resume.skills.length > 0 && (
          <ResumeSection title={sectionTitles.skills} variant="default">
            <div className="">
              <SkillsSection skills={resume.skills} />
            </div>
          </ResumeSection>
        )}

        {/* Certifications Section */}
        {resume.certifications && resume.certifications.length > 0 && (
          <ResumeSection title={sectionTitles.certifications} variant="default">
            <div className={getGridClasses(2) + " gap-4 md:gap-6"}>
              {resume.certifications.map((cert) => (
                <CertificationItem
                  key={cert.id}
                  className="certification-item"
                  certification={cert}
                  showCredentialId={false}
                />
              ))}
            </div>
          </ResumeSection>
        )}

        {/* Languages Section */}
        {resume.languages && resume.languages.length > 0 && (
          <ResumeSection title={sectionTitles.languages} variant="default">
            <div className={getGridClasses(2) + " gap-3 md:gap-4"}>
              {resume.languages.map((lang) => (
                <LanguageItem key={lang.id} language={lang} showBars={false} showLevel={true} />
              ))}
            </div>
          </ResumeSection>
        )}

        {/* Awards Section */}
        {resume.awards && resume.awards.length > 0 && (
          <ResumeSection title={sectionTitles.awards} variant="default">
            <div className="space-y-2 md:space-y-3">
              {resume.awards.map((award) => (
                <div key={award.id} className="award-item flex flex-col md:flex-row md:justify-between md:items-start">
                  <div className="flex-1">
                    <h4 className={`font-bold text-gray-900 ${getTextClasses("sm")}`}>{award.title}</h4>
                    <p className={`text-gray-700 ${getTextClasses("sm")}`}>{award.issuer}</p>
                    {award.description && (
                      <div
                        dangerouslySetInnerHTML={{ __html: award.description }}
                        className={`text-gray-700 ${getTextClasses("sm")} mt-1`}
                      />
                    )}
                  </div>
                  {award.dateReceived && (
                    <span className={`text-gray-600 ${getTextClasses("sm")} mt-1 md:mt-0`}>
                      {formatDate(award.dateReceived, false, locale)}
                    </span>
                  )}
                </div>
              ))}
            </div>
          </ResumeSection>
        )}

        {/* Volunteering Section */}
        {resume.volunteerings && resume.volunteerings.length > 0 && (
          <ResumeSection title={sectionTitles.volunteering} variant="default">
            <div className="space-y-3 md:space-y-4">
              {resume.volunteerings.map((volunteering) => (
                <div key={volunteering.id} className="volunteering-item">
                  <div className="flex flex-col md:flex-row md:justify-between md:items-start mb-1">
                    <div className="flex-1">
                      <h4 className="font-bold text-gray-900">{volunteering.role}</h4>
                      <p className={`text-gray-700 ${getTextClasses("sm")}`}>{volunteering.organization}</p>
                    </div>
                    {(volunteering.startDate || volunteering.endDate) && (
                      <span className={`text-gray-600 ${getTextClasses("sm")} mt-1 md:mt-0`}>
                        {formatDateRange(volunteering.startDate, volunteering.endDate, 0, locale)}
                      </span>
                    )}
                  </div>
                  {volunteering.description && (
                    <div
                      dangerouslySetInnerHTML={{
                        __html: volunteering.description,
                      }}
                      className={`text-gray-700 ${getTextClasses("sm")} mt-2`}
                    />
                  )}
                </div>
              ))}
            </div>
          </ResumeSection>
        )}

        {/* Hobbies Section */}
        {resume.hobbies && resume.hobbies.length > 0 && (
          <ResumeSection title={sectionTitles.hobbies} variant="default">
            <p className={`text-gray-700 ${getTextClasses("sm")} leading-relaxed`}>
              {resume.hobbies.map((hobby) => hobby.name).join(", ")}
            </p>
          </ResumeSection>
        )}

        {/* References Section */}
        {resume.references && resume.references.length > 0 ? (
          <ResumeSection title={sectionTitles.references} variant="default">
            <div className={`${getGridClasses(2)} gap-3 md:gap-4`}>
              {resume.references.map((reference) => (
                <ReferenceItem
                  key={reference.id}
                  reference={reference}
                  showIcons={true}
                  textSize="sm"
                />
              ))}
            </div>
          </ResumeSection>
        ) : (
          <section className={getSectionSpacing()} aria-labelledby="section-references">
            <h2
              id="section-references"
              className={`${getTitleClasses(2)} text-gray-900 mb-3 pb-1 border-b border-gray-300`}
            >
              {sectionTitles.references}
            </h2>
            <div className="section-content">
              <p className={`text-gray-600 ${getTextClasses("sm")}`}>Available upon request</p>
            </div>
          </section>
        )}
      </main>
    </div>
  );
};

export default BronzorTemplate;
