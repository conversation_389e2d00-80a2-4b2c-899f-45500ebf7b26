import React from "react";
import { Reference } from "@/db/schema";

export interface ReferenceItemProps {
  reference: Reference;
  className?: string;
  showIcons?: boolean;
  textSize?: 'sm' | 'base';
}

export const ReferenceItem: React.FC<ReferenceItemProps> = ({
  reference,
  className = "",
  showIcons = true,
  textSize = 'sm'
}) => {
  const textClass = textSize === 'sm' ? 'text-sm' : 'text-base';

  return (
    <div className={`reference-item ${className}`}>
      <h4 className="font-bold text-gray-900">{reference.name}</h4>
      <p className={`text-gray-700 ${textClass}`}>
        {reference.position} at {reference.company}
      </p>
      <div className={`text-gray-600 ${textClass} mt-1`}>
        {reference.email && (
          <p className="flex items-center gap-1">
            {showIcons && <span>✉️</span>}
            <span className="break-all">{reference.email}</span>
          </p>
        )}
        {reference.phone && (
          <p className="flex items-center gap-1">
            {showIcons && <span>📞</span>}
            <span>{reference.phone}</span>
          </p>
        )}
      </div>
      {reference.description && (
        <div
          dangerouslySetInnerHTML={{
            __html: reference.description,
          }}
          className={`text-gray-700 ${textClass} mt-2`}
        />
      )}
    </div>
  );
};