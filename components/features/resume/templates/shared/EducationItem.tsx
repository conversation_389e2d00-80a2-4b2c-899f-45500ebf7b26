import React from "react";
import { Education } from "@/db/schema";
import { EducationDateRange } from "./DateDisplay";
import { formatLocation } from "./utils";

export interface EducationItemProps {
  education: Education;
  variant?: "standard" | "compact" | "europass";
  className?: string;
  locale?: string;
}

export const EducationItem: React.FC<EducationItemProps> = ({
  education,
  variant = "standard",
  className = "",
  locale = "en-US",
}) => {
  const location = formatLocation(education.city || "", education.country || "");
  const degreeText = education.fieldOfStudy ? `${education.degree} in ${education.fieldOfStudy}` : education.degree;

  if (variant === "europass") {
    return (
      <article className={`education-item mb-4 flex ${className}`}>
        <div className="w-1/3 pr-4">
          <EducationDateRange
            startDate={education.startDate}
            endDate={education.endDate}
            isCurrent={education.isCurrent}
            locale={locale}
            className="text-blue-500 font-medium education-dates"
          />
        </div>
        <div className="flex-1">
          <h3 className="font-bold text-gray-900 education-degree">{degreeText}</h3>
          <h4 className="text-gray-700 font-medium education-institution">{education.institution}</h4>
          {location && <p className="text-gray-600 text-sm education-location">{location}</p>}
          {education.description && (
            <div
              dangerouslySetInnerHTML={{ __html: education.description }}
              className="text-gray-700 text-sm mt-2 education-description"
            />
          )}
        </div>
      </article>
    );
  }

  return (
    <article className={`education-item mb-4 ${className}`}>
      <div className="flex justify-between items-start education-header">
        <div>
          <h3 className="font-bold text-gray-900 education-degree">{degreeText}</h3>
          <h4 className="text-gray-700 text-sm education-institution">{education.institution}</h4>
          {location && <p className="text-gray-600 text-sm education-location">{location}</p>}
        </div>
        <div className="text-right text-sm text-gray-600">
          <EducationDateRange
            startDate={education.startDate}
            endDate={education.endDate}
            isCurrent={education.isCurrent}
            locale={locale}
            className="education-dates"
          />
        </div>
      </div>
      {education.description && (
        <div
          dangerouslySetInnerHTML={{ __html: education.description }}
          className="text-gray-700 text-sm mt-2 education-description"
        />
      )}
    </article>
  );
};
