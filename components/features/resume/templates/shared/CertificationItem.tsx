import React from "react";
import { Certification } from "@/db/schema";
import { CertificationDate } from "./DateDisplay";

export interface CertificationItemProps {
  certification: Certification;
  showCredentialId?: boolean;
  className?: string;
}

export const CertificationItem: React.FC<CertificationItemProps> = ({
  certification,
  showCredentialId = true,
  className = "",
}) => {
  const hasIssueDate = Boolean(certification.dateReceived);

  return (
    <div className={`certification-item mb-3 ${className}`}>
      <div className="flex justify-between items-start">
        <div>
          <h3 className="font-bold text-gray-900">{certification.title}</h3>
          <p className="text-gray-700 text-sm">{certification.issuer}</p>
          {hasIssueDate && <CertificationDate date={certification.dateReceived} yearOnly={true} locale="en-US" />}
        </div>
      </div>
      {showCredentialId && certification.description && (
        <div dangerouslySetInnerHTML={{ __html: certification.description }} className="text-gray-700 text-sm mt-2" />
      )}
    </div>
  );
};
