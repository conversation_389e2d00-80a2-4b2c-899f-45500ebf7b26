import React from "react";
import { createSemanticDateRange, createSemanticTimeElement, DateFormat } from "@/lib/shared/date-utils";

// Props for single date display
export interface DateDisplayProps {
  date: string | null | undefined;
  format?: DateFormat;
  locale?: string;
  className?: string;
  showPresent?: boolean;
  presentText?: string;
  yearOnly?: boolean;
}

// Props for date range display
export interface DateRangeDisplayProps {
  startDate: string | null | undefined;
  endDate: string | null | undefined;
  isCurrent?: number | boolean | null;
  format?: DateFormat;
  locale?: string;
  className?: string;
  presentText?: string;
  showDuration?: boolean;
  separator?: "-" | "–" | "to" | "/";
}

/**
 * Semantic date display component with ATS-friendly time elements
 * Renders dates with proper datetime attributes for machine readability
 */
export const DateDisplay: React.FC<DateDisplayProps> = ({
  date,
  format = "short",
  locale = "en-US",
  className = "",
  showPresent = false,
  presentText,
  yearOnly = false,
}) => {
  // Handle present case without date
  if (!date && showPresent) {
    const text = presentText || (locale === "ar" ? "حتى الآن" : "Present");
    return <span className={className}>{text}</span>;
  }

  if (!date) return null;

  const { dateTime, display } = createSemanticTimeElement(date, undefined, yearOnly ? "year-only" : format, locale);

  if (!display) return null;

  return (
    <time dateTime={dateTime} className={className}>
      {display}
    </time>
  );
};

/**
 * Semantic date range display component with ATS-friendly time elements
 * Renders date ranges with proper datetime attributes for machine readability
 */
export const DateRangeDisplay: React.FC<DateRangeDisplayProps> = ({
  startDate,
  endDate,
  isCurrent = false,
  format = "short",
  locale = "en-US",
  className = "",
  presentText,
  showDuration = false,
  separator = "–",
}) => {
  const { startDateTime, endDateTime, displayText, isRange } = createSemanticDateRange(
    startDate,
    endDate,
    isCurrent,
    locale,
    {
      format,
      separator,
      presentText,
    },
  );

  if (!displayText) return null;

  // Single date or present case
  if (!isRange || !endDateTime) {
    return (
      <span className={className}>
        {startDateTime && startDate ? <time dateTime={startDateTime}>{displayText}</time> : displayText}
      </span>
    );
  }

  // Date range case
  const [startDisplay, endDisplay] = displayText.split(` ${separator} `);

  return (
    <span className={className}>
      <time dateTime={startDateTime}>{startDisplay}</time>
      <span> {separator} </span>
      <time dateTime={endDateTime}>{endDisplay}</time>
    </span>
  );
};

/**
 * Compact date display for tight spaces
 * Uses abbreviated formats and minimal spacing
 */
export const CompactDateDisplay: React.FC<DateDisplayProps> = (props) => {
  return <DateDisplay {...props} format="short" className={`text-xs ${props.className || ""}`} />;
};

/**
 * Compact date range display for tight spaces
 * Uses abbreviated formats and minimal spacing
 */
export const CompactDateRangeDisplay: React.FC<DateRangeDisplayProps> = (props) => {
  return <DateRangeDisplay {...props} format="short" className={`text-xs ${props.className || ""}`} />;
};

/**
 * Experience-specific date range with consistent styling
 * Optimized for professional experience sections
 */
export const ExperienceDateRange: React.FC<DateRangeDisplayProps> = (props) => {
  return (
    <DateRangeDisplay
      {...props}
      format="short"
      className={`text-sm text-gray-600 font-medium ${props.className || ""}`}
    />
  );
};

/**
 * Education-specific date range with consistent styling
 * Optimized for education sections
 */
export const EducationDateRange: React.FC<DateRangeDisplayProps> = (props) => {
  return <DateRangeDisplay {...props} format="short" className={`text-sm text-gray-600 ${props.className || ""}`} />;
};

/**
 * Project-specific date range with consistent styling
 * Optimized for project sections
 */
export const ProjectDateRange: React.FC<DateRangeDisplayProps> = (props) => {
  return <DateRangeDisplay {...props} format="short" className={`text-sm text-gray-500 ${props.className || ""}`} />;
};

/**
 * Award/Certification date display with consistent styling
 * For single dates like awards and certifications
 */
export const CertificationDate: React.FC<DateDisplayProps> = (props) => {
  return (
    <DateDisplay
      {...props}
      format="short"
      yearOnly={true}
      className={`text-sm text-gray-600 font-medium ${props.className || ""}`}
    />
  );
};

/**
 * Volunteering date range with consistent styling
 * Optimized for volunteer experience sections
 */
export const VolunteeringDateRange: React.FC<DateRangeDisplayProps> = (props) => {
  return <DateRangeDisplay {...props} format="short" className={`text-sm text-gray-600 ${props.className || ""}`} />;
};
