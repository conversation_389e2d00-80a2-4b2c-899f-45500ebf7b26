/* Resume Templates Global Styles - Responsive & Print Optimized */

/* Base responsive styles for all resume templates */
.resume-template-base {
  /* Default font size optimized for readability */
  font-size: 14px;
  line-height: 1.4;

  /* Ensure proper text rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* A4 Page Sizing */
.a4-page {
  width: 210mm;
  min-height: 297mm;
  margin: 0 auto;
  background: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

/* Responsive breakpoints */
/* Mobile: 320px - 767px */
@media (max-width: 767px) {
  .resume-template-base {
    font-size: 12px;
    line-height: 1.3;
  }

  .a4-page {
    width: 100%;
    min-width: 320px;
    min-height: auto;
    box-shadow: none;
    padding: 16px;
  }

  /* Stack two-column layouts vertically */
  .two-column-layout {
    flex-direction: column !important;
  }

  .two-column-layout .sidebar-content,
  .two-column-layout .main-content {
    width: 100% !important;
  }

  /* Sidebar adjustments for mobile */
  .sidebar-content {
    order: 2;
    margin-top: 20px;
    padding: 16px !important;
  }

  .main-content {
    order: 1;
    padding: 0 !important;
  }

  /* Header adjustments */
  .resume-header {
    text-align: center !important;
    padding: 16px 0 !important;
  }

  .resume-header .header-layout {
    flex-direction: column !important;
    align-items: center !important;
    gap: 16px !important;
  }

  /* Contact info mobile adjustments */
  .contact-section {
    flex-direction: column !important;
    align-items: center !important;
    gap: 8px !important;
  }

  .contact-section > div {
    width: 100%;
    justify-content: center;
  }

  /* Photo adjustments */
  .resume-photo {
    width: 80px !important;
    height: 100px !important;
    margin: 0 auto 16px !important;
  }

  /* Grid layouts */
  .grid-cols-2 {
    grid-template-columns: 1fr !important;
  }

  .grid-cols-3 {
    grid-template-columns: 1fr !important;
  }

  /* Section spacing */
  .resume-section {
    margin-bottom: 20px !important;
  }

  /* Title sizing */
  h1 {
    font-size: 24px !important;
  }

  h2 {
    font-size: 18px !important;
  }

  h3 {
    font-size: 16px !important;
  }

  .section-title {
    font-size: 16px !important;
  }
}

/* Tablet: 768px - 1023px */
@media (min-width: 768px) and (max-width: 1023px) {
  .resume-template-base {
    font-size: 13px;
    line-height: 1.35;
  }

  .a4-page {
    width: 100%;
    max-width: 700px;
    padding: 24px;
  }

  /* Adjust grid layouts for tablet */
  .grid-cols-3 {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  /* Sidebar width adjustments */
  .two-column-layout .sidebar-content {
    width: 35% !important;
  }

  .two-column-layout .main-content {
    width: 65% !important;
  }

  /* Header photo sizing */
  .resume-photo {
    width: 90px !important;
    height: 115px !important;
  }
}

/* Desktop: 1024px and up */
@media (min-width: 1024px) {
  .resume-template-base {
    font-size: 14px;
    line-height: 1.4;
  }

  .a4-page {
    width: 210mm;
    padding: 32px;
  }
}

/* Print Styles */
@media print {
  /* Reset page margins and force A4 size */
  @page {
    size: A4;
    margin: 10mm;
  }

  /* Base print settings */
  .resume-template-base {
    font-size: 11pt !important;
    line-height: 1.3 !important;
    color: #000 !important;
    background: white !important;
  }

  .a4-page {
    width: 100% !important;
    min-height: auto !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    box-shadow: none !important;
    page-break-after: auto;
  }

  /* Hide non-essential elements */
  .no-print,
  .print-hidden,
  button,
  .button,
  input,
  textarea,
  select,
  .interactive-element {
    display: none !important;
  }

  /* Force layout for print */
  .two-column-layout {
    display: flex !important;
    flex-direction: row !important;
  }

  .sidebar-content,
  .main-content {
    page-break-inside: avoid;
  }

  /* Section management */
  .resume-section {
    page-break-inside: avoid;
    break-inside: avoid;
    margin-bottom: 12pt !important;
  }

  .resume-section:last-child {
    margin-bottom: 0 !important;
  }

  /* Header print optimization */
  .resume-header {
    page-break-after: avoid;
    margin-bottom: 16pt !important;
  }

  /* Experience/Education items */
  .experience-item,
  .education-item,
  .project-item,
  .award-item,
  .certification-item,
  .reference-item,
  .volunteering-item {
    page-break-inside: avoid;
    break-inside: avoid;
    margin-bottom: 8pt !important;
  }

  /* Typography for print */
  h1 {
    font-size: 18pt !important;
    font-weight: bold !important;
    margin-bottom: 6pt !important;
  }

  h2,
  .section-title {
    font-size: 14pt !important;
    font-weight: bold !important;
    margin-bottom: 6pt !important;
    margin-top: 12pt !important;
  }

  h3 {
    font-size: 12pt !important;
    font-weight: bold !important;
    margin-bottom: 4pt !important;
  }

  h4 {
    font-size: 11pt !important;
    font-weight: bold !important;
    margin-bottom: 2pt !important;
  }

  p,
  div,
  span {
    font-size: 10pt !important;
    line-height: 1.2 !important;
  }

  /* Contact information */
  .contact-section {
    font-size: 10pt !important;
  }

  /* Photo sizing for print */
  .resume-photo {
    width: 60pt !important;
    height: 80pt !important;
  }

  /* Links - show URL in print */
  a::after {
    content: " (" attr(href) ")";
    font-size: 9pt;
    color: #666;
  }

  a[href^="mailto:"]::after,
  a[href^="tel:"]::after {
    content: "";
  }

  /* Color optimization for print */
  .text-blue-500,
  .text-teal-500,
  .text-green-500,
  .text-orange-500,
  .text-purple-500 {
    color: #333 !important;
  }

  .bg-blue-500,
  .bg-teal-500,
  .bg-green-600,
  .bg-orange-500,
  .bg-purple-500 {
    background-color: #f5f5f5 !important;
    color: #000 !important;
  }

  /* Borders and dividers */
  .border-b,
  .border-t,
  .border-l,
  .border-r {
    border-color: #666 !important;
  }

  /* Skills and language bars */
  .skill-bar,
  .language-dots {
    display: none !important;
  }

  /* Grid adjustments for print */
  .grid {
    display: block !important;
  }

  .grid > * {
    margin-bottom: 4pt !important;
  }

  /* Sidebar print styles */
  .sidebar-content {
    background: #f8f8f8 !important;
    color: #000 !important;
    padding: 8pt !important;
  }

  .sidebar-content h2,
  .sidebar-content h3,
  .sidebar-content .section-title {
    color: #000 !important;
    border-color: #666 !important;
  }

  /* Spacing adjustments */
  .space-y-6 > * + * {
    margin-top: 6pt !important;
  }

  .space-y-4 > * + * {
    margin-top: 4pt !important;
  }

  .space-y-3 > * + * {
    margin-top: 3pt !important;
  }

  .space-y-2 > * + * {
    margin-top: 2pt !important;
  }

  /* Gap adjustments */
  .gap-8 {
    gap: 8pt !important;
  }

  .gap-6 {
    gap: 6pt !important;
  }

  .gap-4 {
    gap: 4pt !important;
  }

  /* Margin and padding resets */
  .mb-8 {
    margin-bottom: 8pt !important;
  }
  .mb-6 {
    margin-bottom: 6pt !important;
  }
  .mb-4 {
    margin-bottom: 4pt !important;
  }
  .mb-3 {
    margin-bottom: 3pt !important;
  }
  .mb-2 {
    margin-bottom: 2pt !important;
  }
  .mb-1 {
    margin-bottom: 1pt !important;
  }

  .mt-8 {
    margin-top: 8pt !important;
  }
  .mt-6 {
    margin-top: 6pt !important;
  }
  .mt-4 {
    margin-top: 4pt !important;
  }
  .mt-3 {
    margin-top: 3pt !important;
  }
  .mt-2 {
    margin-top: 2pt !important;
  }
  .mt-1 {
    margin-top: 1pt !important;
  }

  .p-8 {
    padding: 8pt !important;
  }
  .p-6 {
    padding: 6pt !important;
  }
  .p-4 {
    padding: 4pt !important;
  }
  .p-3 {
    padding: 3pt !important;
  }
  .p-2 {
    padding: 2pt !important;
  }
  .p-1 {
    padding: 1pt !important;
  }

  /* Remove box shadows and focus states */
  *,
  *::before,
  *::after {
    box-shadow: none !important;
    outline: none !important;
  }

  /* Optimize tables if any */
  table {
    border-collapse: collapse !important;
  }

  th,
  td {
    padding: 2pt 4pt !important;
    border: 1pt solid #666 !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .resume-template-base {
    color: #000;
    background: #fff;
  }

  .text-gray-600,
  .text-gray-700,
  .text-gray-800 {
    color: #000 !important;
  }

  .bg-gray-100,
  .bg-gray-200 {
    background-color: #fff !important;
    border: 1px solid #000 !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
