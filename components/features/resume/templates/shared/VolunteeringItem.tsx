import React from "react";
import { Volunteering } from "@/db/schema";
import { formatDateRange } from "./utils";

export interface VolunteeringItemProps {
  volunteering: Volunteering;
  locale?: string;
  className?: string;
  textSize?: 'sm' | 'base';
  showDateRange?: boolean;
}

export const VolunteeringItem: React.FC<VolunteeringItemProps> = ({
  volunteering,
  locale = 'en-US',
  className = "",
  textSize = 'sm',
  showDateRange = true
}) => {
  const textClass = textSize === 'sm' ? 'text-sm' : 'text-base';

  return (
    <div className={`volunteering-item ${className}`}>
      <div className="flex justify-between items-start mb-1">
        <div>
          <h4 className="font-bold text-gray-900">{volunteering.role}</h4>
          <p className={`text-gray-700 ${textClass}`}>{volunteering.organization}</p>
        </div>
        {showDateRange && (volunteering.startDate || volunteering.endDate) && (
          <span className={`text-gray-600 ${textClass}`}>
            {formatDateRange(volunteering.startDate, volunteering.endDate, 0, locale)}
          </span>
        )}
      </div>
      {volunteering.description && (
        <div
          dangerouslySetInnerHTML={{
            __html: volunteering.description,
          }}
          className={`text-gray-700 ${textClass} mt-2`}
        />
      )}
    </div>
  );
};