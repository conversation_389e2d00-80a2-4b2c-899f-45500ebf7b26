import React from "react";
import { Language } from "@/db/schema";

export interface LanguageItemProps {
  language: Language;
  showLevel?: boolean;
  showBars?: boolean;
  showPercentage?: boolean;
  colorScheme?: string;
  barHeight?: 'sm' | 'md' | 'lg';
  textSize?: 'sm' | 'base';
  className?: string;
}

export const LanguageItem: React.FC<LanguageItemProps> = ({
  language,
  showLevel = true,
  showBars = false,
  showPercentage = false,
  colorScheme = 'blue',
  barHeight = 'sm',
  textSize = 'sm',
  className = "",
}) => {
  const getProficiencyLevel = (proficiency: number): string => {
    if (proficiency >= 90) return "Native Speaker";
    if (proficiency >= 70) return "Fluent";
    if (proficiency >= 50) return "Intermediate";
    if (proficiency >= 30) return "Basic";
    return "";
  };

  const getColorClass = (scheme: string) => {
    const colorMap: Record<string, string> = {
      blue: 'bg-blue-700',
      green: 'bg-green-700',
      purple: 'bg-purple-700',
      red: 'bg-red-700',
      orange: 'bg-orange-700',
      teal: 'bg-teal-700',
      pink: 'bg-pink-700',
      indigo: 'bg-indigo-700',
    };
    return colorMap[scheme] || colorMap.blue;
  };

  const getBarHeightClass = (height: string) => {
    const heightMap: Record<string, string> = {
      sm: 'h-2',
      md: 'h-4',
      lg: 'h-6',
    };
    return heightMap[height] || heightMap.sm;
  };

  const textClass = textSize === 'sm' ? 'text-sm' : 'text-base';
  const nameTextClass = textSize === 'sm' ? 'text-base' : 'text-lg';

  return (
    <div className={`language-item ${className}`}>
      <div className="flex justify-between items-center mb-2">
        <span className={`font-bold text-gray-800 ${nameTextClass}`}>{language.name}</span>
        {showLevel && (
          <span className={`text-gray-600 ${textClass}`}>{getProficiencyLevel(language.proficiency)}</span>
        )}
        {showPercentage && (
          <span className={`font-bold text-gray-600 ${textClass}`}>{language.proficiency}%</span>
        )}
      </div>
      {showBars && (
        <div className={`w-full bg-gray-300 ${getBarHeightClass(barHeight)}`}>
          <div
            className={`${getBarHeightClass(barHeight)} ${getColorClass(colorScheme)}`}
            style={{ width: `${language.proficiency}%` }}
          />
        </div>
      )}
    </div>
  );
};
