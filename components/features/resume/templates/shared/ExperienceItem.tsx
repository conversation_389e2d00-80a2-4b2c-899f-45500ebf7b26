import React from "react";
import { Experience } from "@/db/schema";
import { ExperienceDateRange } from "./DateDisplay";
import { formatLocation } from "./utils";

export interface ExperienceItemProps {
  experience: Experience;
  variant?: "standard" | "compact" | "detailed" | "europass";
  showWebsiteIcon?: boolean;
  className?: string;
  locale?: string;
}

export const ExperienceItem: React.FC<ExperienceItemProps> = ({
  experience,
  variant = "standard",
  showWebsiteIcon: _showWebsiteIcon = true,
  className = "",
  locale = "en-US",
}) => {
  const location = formatLocation(experience.city || "", experience.country || "");

  if (variant === "europass") {
    return (
      <article className={`experience-item mb-4 flex ${className}`}>
        <div className="w-1/3 pr-4">
          <ExperienceDateRange
            startDate={experience.startDate}
            endDate={experience.endDate}
            isCurrent={experience.isCurrent}
            locale={locale}
            className="text-blue-500 font-medium experience-dates"
          />
        </div>
        <div className="flex-1">
          <h3 className="font-bold text-gray-900 experience-title">{experience.title}</h3>
          <h4 className="text-gray-700 font-medium experience-company">{experience.company}</h4>
          {location && <p className="text-gray-600 text-sm experience-location">{location}</p>}
          {experience.description && (
            <div
              dangerouslySetInnerHTML={{ __html: experience.description }}
              className="text-gray-700 text-sm mt-2 experience-description"
            />
          )}
        </div>
      </article>
    );
  }

  return (
    <article className={`experience-item mb-4 ${className}`}>
      <div className="flex justify-between items-start mb-2 experience-header">
        <div className="flex-1">
          <h3 className="font-bold text-gray-900 text-base experience-title">{experience.title}</h3>
          <h4 className="text-gray-700 text-sm font-semibold experience-company">{experience.company}</h4>
        </div>
        <div className="text-right text-sm text-gray-600">
          <ExperienceDateRange
            startDate={experience.startDate}
            endDate={experience.endDate}
            isCurrent={experience.isCurrent}
            locale={locale}
            className="experience-dates"
          />
          {location && <p className="experience-location">{location}</p>}
        </div>
      </div>
      {experience.description && (
        <div
          dangerouslySetInnerHTML={{ __html: experience.description }}
          className="text-gray-700 text-sm mt-2 experience-description"
        />
      )}
    </article>
  );
};
