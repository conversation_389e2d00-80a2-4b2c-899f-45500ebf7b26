"use client";

import { <PERSON><PERSON>, ModalBody, ModalContent, useDisclosure } from "@heroui/react";
import { useToPng } from "@hugocxl/react-to-image";
import React, { useEffect, useState } from "react";
import { saveResumeThumbnail } from "@/actions/resumes";
import { trpc } from "@/app/_trpc/client";
import { UpgradeModal } from "@/components/payment";
import { COLOR_SCHEMES, ColorSchemeId } from "@/config/color-schemes";
import { ResumePreviewControls } from "./resume-preview-controls";
import ResumeTemplateRenderer from "./templates/template-registry";

interface ResumePreviewProps {
  resumeId: number;
  className?: string;
  showTemplateSelector?: boolean;
  showControls?: boolean;
}

const ResumePreview: React.FC<ResumePreviewProps> = ({ resumeId, className = "", showControls = true }) => {
  const getFullResume = trpc.resumes.getFullResume.useQuery({ id: resumeId });
  const { isLoading: isLoadingFullResume, data: resume } = getFullResume;
  const { isOpen: isFullScreenOpen, onOpenChange: onFullScreenOpenChange } = useDisclosure();
  const { isOpen: isUpgradeOpen, onOpen: onUpgradeOpen, onClose: onUpgradeClose } = useDisclosure();
  // React Compiler will optimize these calculations automatically
  const schemeId = (resume?.colorScheme || "blue") as ColorSchemeId;
  const colorScheme = COLOR_SCHEMES[schemeId] || COLOR_SCHEMES.blue;

  const cssVariables = {
    "--resume-background": colorScheme.background,
    "--resume-foreground": colorScheme.text,
    "--resume-primary": colorScheme.primary,
  };

  const [exportError, setExportError] = useState<string | null>(null);
  const elementRef = React.useRef<HTMLDivElement>(null);

  const [{ isLoading: isConverting }, convert, ref] = useToPng<HTMLDivElement>({
    selector: "#resume-preview",
    onSuccess: async (data) => {
      try {
        const _result = await saveResumeThumbnail(resumeId, data);
      } catch (error: unknown) {
        throw new Error(error instanceof Error ? error.message : "Failed to save thumbnail");
      }
    },
    onError: (error: unknown) => {
      throw new Error(error instanceof Error ? error.message : "Failed to convert resume to image");
    },
  });

  useEffect(() => {
    // Wait for the component to be fully rendered before generating image
    const timer = setTimeout(() => {
      convert();
    }, 1500); // Wait 1.5 seconds for rendering

    return () => clearTimeout(timer);
  }, [resume]);

  // Server-side PDF generation
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);

  const locale = "en";

  const generatePDF = async () => {
    try {
      setIsGeneratingPDF(true);
      setExportError(null);

      const response = await fetch("/api/pdf", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ resumeId, locale }),
      });

      if (!response.ok) {
        if (response.status === 403) {
          // Premium required - open upgrade modal
          onUpgradeOpen();
          return;
        }
        throw new Error("Failed to generate PDF");
      }

      // Create blob and download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `${resume?.title || "resume"}_${new Date().toISOString().slice(0, 10)}.pdf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      setExportError(error instanceof Error ? error.message : "Failed to generate PDF");
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  const handleDownloadPDF = async () => {
    await generatePDF();
  };

  return (
    <div className={`resume-preview flex flex-col h-full  ${className}`} style={cssVariables as React.CSSProperties}>
      {/* Controls */}
      {showControls && (
        <ResumePreviewControls
          resumeId={resumeId}
          exportError={exportError}
          exportPdf={handleDownloadPDF}
          handleFullScreen={onFullScreenOpenChange}
          isGeneratingPDF={isGeneratingPDF}
        />
      )}

      {/* Preview Area */}
      <div className="preview-area flex-1 overflow-auto bg-gray-100 dark:bg-gray-900 p-4">
        <div className="preview-container flex justify-center">
          <div
            ref={elementRef}
            className="resume-preview-content bg-white dark:bg-gray-800 shadow-lg dark:shadow-gray-900/30 border dark:border-gray-700"
          >
            <div ref={ref} className="resume-content-wrapper" id="resume-preview">
              {resume && <ResumeTemplateRenderer className="w-full h-full" resume={resume} />}
            </div>
          </div>
        </div>
      </div>

      <Modal
        closeButton
        classNames={{
          base: "p-0",
          wrapper: "items-start justify-start",
          body: "p-0",
          closeButton: "z-50 absolute top-4 right-4 text-xl right-4  ",
        }}
        isOpen={isFullScreenOpen}
        scrollBehavior="inside"
        size="full"
        onOpenChange={onFullScreenOpenChange}
      >
        <ModalContent>
          <ModalBody className="p-0">
            {/* Preview Area */}
            <div className="preview-area flex-1 overflow-auto bg-gray-100 dark:bg-gray-900 p-4">
              <div className="preview-container flex justify-center">
                <div
                  className="resume-preview-content bg-white dark:bg-gray-800 shadow-lg dark:shadow-gray-900/30 border dark:border-gray-700"
                  style={{
                    transformOrigin: "top center",
                    width: "8.5in",
                    minHeight: "11in",
                    margin: "0 auto",
                  }}
                >
                  {resume && <ResumeTemplateRenderer className="w-full h-full" resume={resume} />}
                </div>
              </div>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* Upgrade Modal */}
      <UpgradeModal isOpen={isUpgradeOpen} onClose={onUpgradeClose} feature="pdf" />
    </div>
  );
};

export default ResumePreview;
