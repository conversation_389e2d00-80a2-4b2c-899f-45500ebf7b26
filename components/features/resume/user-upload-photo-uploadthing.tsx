"use client";

import { <PERSON><PERSON>, But<PERSON>, Progress } from "@heroui/react";
import { Icon } from "@iconify/react";
import React from "react";
import { toast } from "react-hot-toast";
import { useFileUpload } from "@/hooks/use-file-upload";
import { getInitials } from "@/lib/utils";

interface UserPhotoUploadProps {
  photo: string | null;
  resumeId: number;
  onPhotoUpdate?: (photoUrl: string | null) => void;
  initials?: string;
}

export const UserPhotoUploadThing: React.FC<UserPhotoUploadProps> = ({
  photo,
  resumeId: _resumeId,
  onPhotoUpdate,
  initials,
}) => {
  const [image, setImage] = React.useState<string | null>(photo);
  const [isDeleting, setIsDeleting] = React.useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  // Update image when photo prop changes (after form submission)
  React.useEffect(() => {
    if (photo && photo !== image) {
      setImage(photo);
    }
  }, [photo]);

  const { uploadSingleFile, isUploading, uploadProgress } = useFileUpload({
    endpoint: "resumePhotoUploader",
    onSuccess: async (res) => {
      if (res && res.length > 0) {
        const photoUrl = res[0].url;
        setImage(photoUrl);
        onPhotoUpdate?.(photoUrl);
        toast.success("Photo uploaded successfully!");
      }
    },
    onError: (error) => {
      console.error("Upload error:", error);
    },
  });

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      uploadSingleFile(file);
    }
  };

  const handleDelete = async () => {
    if (!image) return;

    setIsDeleting(true);
    try {
      // Delete from UploadThing first
      const response = await fetch("/api/uploadthing/delete", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ fileUrl: image }),
      });

      if (!response.ok) {
        throw new Error("Failed to delete file from storage");
      }

      // Clear local state after successful deletion
      setImage(null);
      onPhotoUpdate?.(null);
      toast.success("Photo deleted successfully!");

      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error) {
      console.error("Error deleting photo:", error);
      toast.error("Failed to delete photo");
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="flex flex-col items-center gap-4">
      {image ? (
        <Avatar className="w-24 h-24" src={image} />
      ) : (
        <Avatar className="w-24 h-24" name={getInitials(initials || "User Name")} />
      )}
      {isUploading && (
        <div className="w-full max-w-md">
          <Progress
            aria-label="Upload progress"
            color="primary"
            size="sm"
            value={uploadProgress}
            showValueLabel={true}
            className="w-full"
          />
        </div>
      )}
      <div className="flex gap-4">
        <Button
          isIconOnly
          as="label"
          color="primary"
          isDisabled={isUploading || isDeleting}
          isLoading={isUploading}
          size="sm"
          startContent={!isUploading && <Icon icon="lucide:upload" />}
          title="Upload photo"
        >
          <input
            ref={fileInputRef}
            accept="image/*"
            className="hidden"
            disabled={isUploading || isDeleting}
            type="file"
            onChange={handleFileChange}
          />
        </Button>
        <Button
          isIconOnly
          color="danger"
          isDisabled={!image || isUploading || isDeleting}
          isLoading={isDeleting}
          size="sm"
          startContent={!isDeleting && <Icon icon="lucide:trash-2" />}
          title="Delete photo"
          variant="flat"
          onPress={handleDelete}
        />
      </div>
    </div>
  );
};
