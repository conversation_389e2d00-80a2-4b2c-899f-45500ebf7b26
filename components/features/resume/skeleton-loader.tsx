"use client";

import { Card, CardBody, Skeleton } from "@heroui/react";
import { Icon } from "@iconify/react";
import { motion } from "framer-motion";

interface SkeletonLoaderProps {
  // Layout
  count?: number;
  gridCols?: 1 | 2 | 3 | 4 | 5;
  className?: string;

  // Card options
  variant?: "card" | "list" | "custom";
  cardClassName?: string;

  // Skeleton configuration
  showImage?: boolean;
  imageHeight?: string;
  showTitle?: boolean;
  titleWidth?: string;
  showSubtitle?: boolean;
  subtitleWidth?: string;
  showBody?: boolean;
  bodyLines?: number;

  // Animation
  animationDelay?: boolean;

  // Custom content
  children?: React.ReactNode;
}

const GRID_CLASSES = {
  1: "grid-cols-1",
  2: "grid-cols-1 sm:grid-cols-2",
  3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
  4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
  5: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5",
};

export default function SkeletonLoader({
  count = 6,
  gridCols = 3,
  className = "",
  variant = "card",
  cardClassName = "",
  showImage = true,
  imageHeight = "h-[320px]",
  showTitle = true,
  titleWidth = "w-3/4",
  showSubtitle = true,
  subtitleWidth = "w-1/2",
  showBody = false,
  bodyLines = 3,
  animationDelay = true,
  children,
}: SkeletonLoaderProps) {
  const renderCardSkeleton = (index: number) => (
    <motion.div
      key={index}
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{
        duration: 0.5,
        delay: animationDelay ? index * 0.1 : 0,
        ease: "easeOut",
      }}
      className={`w-full ${gridCols >= 4 ? "max-w-[280px]" : ""}`}
    >
      <motion.div
        whileHover={{ scale: 1.02, y: -4 }}
        transition={{ duration: 0.2 }}
      >
        <Card
          className={`bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border-0 hover:shadow-xl transition-shadow duration-300 ${cardClassName}`}
        >
          {showImage && (
            <div className="relative overflow-hidden rounded-t-lg">
              <Skeleton className="rounded-lg">
                <div
                  className={`${imageHeight} rounded-lg bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600`}
                ></div>
              </Skeleton>
              {/* Playful shimmer effect */}
              <motion.div
                className="absolute top-2 right-2 w-6 h-6 bg-white/20 rounded-full flex items-center justify-center"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: index * 0.3,
                }}
              >
                <Icon
                  icon="heroicons:sparkles-20-solid"
                  className="w-3 h-3 text-primary"
                />
              </motion.div>
            </div>
          )}
          <CardBody className={showImage ? "pt-4" : "p-4"}>
            {showTitle && (
              <motion.div
                animate={{ opacity: [0.7, 1, 0.7] }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  delay: index * 0.1,
                }}
              >
                <Skeleton className="mb-3 rounded-lg">
                  <div
                    className={`h-6 ${titleWidth} rounded-lg bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600`}
                  ></div>
                </Skeleton>
              </motion.div>
            )}
            {showSubtitle && (
              <motion.div
                animate={{ opacity: [0.6, 0.9, 0.6] }}
                transition={{
                  duration: 1.8,
                  repeat: Infinity,
                  delay: index * 0.1 + 0.2,
                }}
              >
                <Skeleton className="rounded-lg">
                  <div
                    className={`h-4 ${subtitleWidth} rounded-lg bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600`}
                  ></div>
                </Skeleton>
              </motion.div>
            )}
            {showBody && (
              <div className="space-y-2 mt-3">
                {Array.from({ length: bodyLines }).map((_, lineIndex) => (
                  <motion.div
                    key={lineIndex}
                    animate={{ opacity: [0.5, 0.8, 0.5] }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: index * 0.1 + lineIndex * 0.1 + 0.4,
                    }}
                  >
                    <Skeleton className="rounded-lg">
                      <div
                        className={`h-3 ${
                          lineIndex === bodyLines - 1 ? "w-2/3" : "w-full"
                        } rounded-lg bg-gradient-to-r from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600`}
                      ></div>
                    </Skeleton>
                  </motion.div>
                ))}
              </div>
            )}
          </CardBody>
        </Card>
      </motion.div>
    </motion.div>
  );

  const renderListSkeleton = (index: number) => (
    <div
      key={index}
      className={`w-full ${animationDelay ? "animate-pulse" : ""}`}
      style={animationDelay ? { animationDelay: `${index * 50}ms` } : {}}
    >
      <Card
        className={`bg-white/80 dark:bg-gray-800/80 backdrop-blur-md border-0 ${cardClassName}`}
      >
        <CardBody className="p-4">
          <div className="flex items-center gap-4">
            {showImage && (
              <Skeleton className="rounded-lg">
                <div className="h-16 w-16 rounded-lg bg-gray-200 dark:bg-gray-700"></div>
              </Skeleton>
            )}
            <div className="flex-1 space-y-2">
              {showTitle && (
                <Skeleton className="rounded-lg">
                  <div
                    className={`h-5 ${titleWidth} rounded-lg bg-gray-200 dark:bg-gray-700`}
                  ></div>
                </Skeleton>
              )}
              {showSubtitle && (
                <Skeleton className="rounded-lg">
                  <div
                    className={`h-4 ${subtitleWidth} rounded-lg bg-gray-200 dark:bg-gray-700`}
                  ></div>
                </Skeleton>
              )}
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );

  const renderCustomSkeleton = (index: number) => (
    <div
      key={index}
      className={`${animationDelay ? "animate-pulse" : ""}`}
      style={animationDelay ? { animationDelay: `${index * 100}ms` } : {}}
    >
      {children}
    </div>
  );

  const renderSkeleton = (index: number) => {
    switch (variant) {
      case "list":
        return renderListSkeleton(index);
      case "custom":
        return renderCustomSkeleton(index);
      case "card":
      default:
        return renderCardSkeleton(index);
    }
  };

  const containerClasses = [
    "grid gap-6",
    variant === "list" ? "grid-cols-1 gap-4" : GRID_CLASSES[gridCols],
    variant === "card" && gridCols >= 4 ? "justify-items-center" : "",
    className,
  ]
    .filter(Boolean)
    .join(" ");

  return (
    <div className="space-y-6">
      {/* Playful loading header */}
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        {/* Bouncing dots */}
        <div className="flex justify-center space-x-1">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className="w-2 h-2 bg-primary rounded-full"
              animate={{ y: [0, -8, 0] }}
              transition={{
                duration: 0.8,
                repeat: Infinity,
                delay: i * 0.2,
                ease: "easeInOut",
              }}
            />
          ))}
        </div>
      </motion.div>

      {/* Skeleton grid */}
      <div className={containerClasses}>
        {Array.from({ length: count }).map((_, index) => renderSkeleton(index))}
      </div>
    </div>
  );
}

// Convenience wrapper for resume card skeletons
export function ResumeCardSkeleton({
  count = 6,
  className = "",
}: Pick<SkeletonLoaderProps, "count" | "className">) {
  return (
    <SkeletonLoader
      count={count}
      gridCols={5}
      variant="card"
      showImage={true}
      imageHeight="h-[320px]"
      showTitle={true}
      titleWidth="w-3/4"
      showSubtitle={true}
      subtitleWidth="w-1/2"
      className={className}
    />
  );
}

// Convenience wrapper for website card skeletons
export function WebsiteCardSkeleton({
  count = 6,
  className = "",
}: Pick<SkeletonLoaderProps, "count" | "className">) {
  return (
    <SkeletonLoader
      count={count}
      gridCols={3}
      variant="card"
      showImage={false}
      showTitle={true}
      titleWidth="w-2/3"
      showSubtitle={true}
      subtitleWidth="w-1/2"
      showBody={true}
      bodyLines={2}
      className={className}
    />
  );
}

// Convenience wrapper for list item skeletons
export function ListItemSkeleton({
  count = 5,
  className = "",
}: Pick<SkeletonLoaderProps, "count" | "className">) {
  return (
    <SkeletonLoader
      count={count}
      variant="list"
      showImage={true}
      showTitle={true}
      titleWidth="w-1/2"
      showSubtitle={true}
      subtitleWidth="w-1/3"
      className={className}
    />
  );
}
