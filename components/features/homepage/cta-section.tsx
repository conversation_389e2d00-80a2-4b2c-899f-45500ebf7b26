"use client";
import { But<PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { motion } from "framer-motion";
import CreateResumeButton from "@/components/features/resume/createResumeButton";
import { subtitle, title } from "@/components/primitives";

export default function CTASection() {
  return (
    <section className="py-24 md:py-32 bg-default-50 dark:bg-default-950/20">
      <div className="max-w-4xl mx-auto px-4 text-center">
        <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
          <h2 className={title({ size: "lg" })}>Ready to land your dream job?</h2>
          <p className={subtitle({ class: "mt-6 max-w-2xl mx-auto text-lg" })}>
            Don't let a mediocre resume hold you back. Create a professional, ATS-friendly resume in minutes and take
            the next step in your career.
          </p>
          <div className="mt-10">
            <Button
              as="a"
              href={"/resumes"}
              color="primary"
              radius="full"
              size="lg"
              variant="shadow"
              className="font-semibold px-8 py-4"
              startContent={<Icon icon="heroicons:rocket-launch-20-solid" className="w-5 h-5" />}
            >
              Get Started
            </Button>{" "}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
