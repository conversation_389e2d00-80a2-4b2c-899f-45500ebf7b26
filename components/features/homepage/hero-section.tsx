"use client";

import { <PERSON><PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { motion } from "framer-motion";
import { subtitle, title } from "@/components/primitives";
import { QuickCVBrandLogo } from "@/components/logo";

export default function HeroSection() {


  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        staggerChildren: 0.15,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 },
    },
  };

  const floatingVariants = {
    animate: {
      y: [-5, 5, -5],
      rotate: [-1, 1, -1],
      transition: {
        duration: 6,
        repeat: Infinity,
        ease: "easeInOut",
      },
    },
  };

  return (
    <motion.section
      initial="hidden"
      animate="visible"
      variants={containerVariants}
      className="relative flex flex-col items-center justify-center gap-8 py-14 md:py-20 overflow-hidden"
    >
      <div className="absolute inset-0 bg-gradient-to-br from-primary-50 via-white to-secondary-50 dark:from-primary-950/20 dark:via-background dark:to-secondary-950/20" />
  
      {/* Floating animated shapes */}
      <motion.div
        variants={floatingVariants}
        animate="animate"
        className="absolute top-20 left-10 w-64 h-64 bg-primary-100 dark:bg-primary-900/20 rounded-full blur-3xl opacity-40"
      />
      <motion.div
        variants={floatingVariants}
        animate="animate"
        style={{ animationDelay: "2s" }}
        className="absolute bottom-20 right-10 w-80 h-80 bg-secondary-100 dark:bg-secondary-900/20 rounded-full blur-3xl opacity-30"
      />
      <motion.div
        variants={floatingVariants}
        animate="animate"
        style={{ animationDelay: "4s" }}
        className="absolute top-1/3 right-1/4 w-48 h-48 bg-success-200 dark:bg-success-800/20 rounded-2xl blur-3xl opacity-25"
      />

      <div className="relative text-center max-w-4xl z-10 px-4">
        <motion.div variants={itemVariants} className="mb-8">
          <h1
            className={title({ size: "lg", class: " font-bold text-3xl bg-gradient-to-r text-center from-blue-600 to-purple-600 bg-clip-text text-transparent" })}
          >
            QuickCV
          </h1>
        </motion.div>
        <motion.div variants={itemVariants}>
          <h1 className={title({ size: "lg", class: "leading-tight" })}>
            Craft a resume that opens doors to your{" "}
            <span className={title({ color: "violet", size: "lg" })}>dream career</span>
          </h1>
        </motion.div>

        <motion.div variants={itemVariants}>
          <p className={subtitle({ class: "mt-8 max-w-2xl mx-auto text-lg" })}>
            Build beautiful, professional resumes in minutes. Our ATS-friendly templates are designed to get you hired
            faster.
          </p>
        </motion.div>

        <motion.div variants={itemVariants}>
          <div className="flex flex-col sm:flex-row gap-4 justify-center mt-10">
            <Button
              as="a"
              href={"/resumes"}
              color="primary"
              radius="full"
              size="lg"
              variant="shadow"
              className="font-semibold px-8 py-4"
              startContent={<Icon icon="heroicons:rocket-launch-20-solid" className="w-5 h-5" />}
            >
              Get Started
            </Button>
            <Button
              as="a"
              className="font-semibold px-8 py-4"
              href="#templates"
              size="lg"
              variant="bordered"
              startContent={<Icon icon="heroicons:eye-20-solid" className="w-5 h-5" />}
            >
              Explore Templates
            </Button>
          </div>
        </motion.div>
      </div>
    </motion.section>
  );
}
