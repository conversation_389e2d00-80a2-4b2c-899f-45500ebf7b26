"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>, Ta<PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { EmailShareContent } from "./email-share-content";
import { SocialShareButtons } from "./social-share-buttons";
import { EMAIL_MODAL_CONFIG, type EmailShareModalProps } from "@/lib/email-share-utils";


export function ShareModal({ isOpen, onClose, resumeId }: EmailShareModalProps) {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size={EMAIL_MODAL_CONFIG.size}
      scrollBehavior={EMAIL_MODAL_CONFIG.scrollBehavior}
      classNames={EMAIL_MODAL_CONFIG.classNames}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <div className="flex items-center gap-2">
            <Icon icon="tabler:share" className="w-6 h-6" />
            Share Resume
          </div>
          <p className="text-sm text-default-500 font-normal">
            Share your resume with potential employers and track engagement.
          </p>
        </ModalHeader>
        <ModalBody>
          <Tabs
            aria-label="Sharing options"
            color="primary"
            variant="underlined"
            classNames={{
              tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
              cursor: "w-full bg-primary h-0.5",
              tab: "max-w-fit px-4 h-12 data-[selected=true]:text-primary",
              tabContent: "group-data-[selected=true]:text-primary group-data-[selected=true]:font-semibold",
            }}
          >
            <Tab
              key="social"
              title={
                <div className="flex items-center space-x-2">
                  <Icon icon="tabler:share" className="w-4 h-4" />
                  <span>Social Sharing</span>
                </div>
              }
            >
              <div className="py-4">
                <SocialShareButtons resumeId={resumeId} />
              </div>
            </Tab>
            <Tab
              key="email"
              title={
                <div className="flex items-center space-x-2">
                  <Icon icon="tabler:mail" className="w-4 h-4" />
                  <span>Email Templates</span>
                </div>
              }
            >
              <div className="py-4">
                <EmailShareContent resumeId={resumeId} />
              </div>
            </Tab>
          </Tabs>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}
