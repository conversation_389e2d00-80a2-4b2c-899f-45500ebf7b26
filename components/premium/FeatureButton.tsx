import { Button } from "@heroui/react";
import { Icon } from "@iconify/react";
import React from "react";

interface FeatureButtonProps {
  featureId: string;
  onClick?: () => void;
  variant?: "solid" | "bordered" | "light" | "flat" | "faded" | "shadow" | "ghost";
  size?: "sm" | "md" | "lg";
  isIconOnly?: boolean;
  className?: string;
  children?: React.ReactNode;
  startContent?: React.ReactNode;
  endContent?: React.ReactNode;
  icon?: React.ReactNode;
  color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger";
  isDisabled?: boolean;
  disabled?: boolean;
  isLoading?: boolean;
}

export function FeatureButton({
  featureId: _featureId,
  onClick,
  variant = "flat",
  size = "md",
  isIconOnly = false,
  className = "",
  children,
  startContent,
  endContent,
  icon,
  color = "default",
  isDisabled = false,
  disabled = false,
  isLoading = false,
}: FeatureButtonProps) {
  return (
    <Button
      onClick={onClick}
      variant={variant}
      size={size}
      isIconOnly={isIconOnly}
      className={className}
      startContent={startContent || icon}
      endContent={endContent}
      color={color}
      isDisabled={isDisabled || disabled}
      isLoading={isLoading}
    >
      {children}
    </Button>
  );
}
