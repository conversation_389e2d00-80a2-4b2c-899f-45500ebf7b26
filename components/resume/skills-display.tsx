import React from "react";
import { Skill } from "@/db/schema";
import {
  getProficiencyOutOfFive,
  getProficiencyText,
  getSkillsColorClasses,
  groupSkillsByCategory,
} from "@/lib/skills-utils";

interface SkillsDisplayProps {
  skills: Skill[];
  colorScheme: string;
  variant?: "progress-bars" | "dots" | "stars" | "badges" | "simple" | "minimal";
  groupByCategory?: boolean;
  maxColumns?: number;
  showProficiency?: boolean;
}

// Progress Bar Component
const ProgressBarSkill: React.FC<{
  skill: Skill;
  colors: ReturnType<typeof getSkillsColorClasses>;
  showProficiency: boolean;
}> = ({ skill, colors, showProficiency }) => (
  <div className="space-y-2">
    <div className="flex justify-between items-center">
      <span className="text-sm font-medium text-gray-800">{skill.name}</span>
      {showProficiency && skill.proficiency && (
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-600">{skill.proficiency}%</span>
          <span className="text-xs text-gray-500">({getProficiencyText(skill.proficiency)})</span>
        </div>
      )}
    </div>
    {skill.proficiency && (
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={`h-2 rounded-full ${colors.primary.split(" ")[0]}`}
          style={{ width: `${skill.proficiency}%` }}
        />
      </div>
    )}
  </div>
);

// Dots Component
const DotsSkill: React.FC<{
  skill: Skill;
  colors: ReturnType<typeof getSkillsColorClasses>;
  showProficiency: boolean;
}> = ({ skill, colors, showProficiency }) => {
  const filledDots = getProficiencyOutOfFive(skill.proficiency);

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <span className="text-sm font-medium text-gray-800">{skill.name}</span>
        {showProficiency && skill.proficiency && (
          <span className="text-xs text-gray-500">({getProficiencyText(skill.proficiency)})</span>
        )}
      </div>
      <div className="flex gap-1">
        {Array.from({ length: 5 }).map((_, index) => (
          <div
            key={index}
            className={`w-3 h-3 rounded-full ${index < filledDots ? colors.primary.split(" ")[0] : "bg-gray-200"}`}
          />
        ))}
      </div>
    </div>
  );
};

// Stars Component
const StarsSkill: React.FC<{
  skill: Skill;
  colors: ReturnType<typeof getSkillsColorClasses>;
  showProficiency: boolean;
}> = ({ skill, colors, showProficiency }) => {
  const filledStars = getProficiencyOutOfFive(skill.proficiency);

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <span className="text-sm font-medium text-gray-800">{skill.name}</span>
        {showProficiency && skill.proficiency && (
          <span className="text-xs text-gray-500">({getProficiencyText(skill.proficiency)})</span>
        )}
      </div>
      <div className="flex gap-1">
        {Array.from({ length: 5 }).map((_, index) => (
          <svg
            key={index}
            className={`w-4 h-4 ${index < filledStars ? colors.primary.split(" ")[1] : "text-gray-300"}`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
    </div>
  );
};

// Badges Component
const BadgesSkill: React.FC<{
  skill: Skill;
  colors: ReturnType<typeof getSkillsColorClasses>;
  showProficiency: boolean;
}> = ({ skill, colors, showProficiency }) => {
  const proficiencyText = skill.proficiency ? getProficiencyText(skill.proficiency) : "";

  return (
    <div className="inline-flex items-center gap-2">
      <span className={`px-3 py-1 rounded-full text-sm font-medium ${colors.secondary}`}>{skill.name}</span>
      {showProficiency && proficiencyText && (
        <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs">{proficiencyText}</span>
      )}
    </div>
  );
};

// Simple List Component
const SimpleSkill: React.FC<{
  skill: Skill;
  colors: ReturnType<typeof getSkillsColorClasses>;
  showProficiency: boolean;
}> = ({ skill, colors, showProficiency }) => (
  <div className="flex justify-between items-center py-1">
    <span className="text-sm font-medium text-gray-800">{skill.name}</span>
    {showProficiency && skill.proficiency && (
      <span className="text-xs text-gray-600">
        {skill.proficiency}% ({getProficiencyText(skill.proficiency)})
      </span>
    )}
  </div>
);

// Minimal Component
const MinimalSkill: React.FC<{
  skill: Skill;
  colors: ReturnType<typeof getSkillsColorClasses>;
  showProficiency: boolean;
}> = ({ skill, colors, showProficiency }) => (
  <div className="space-y-1">
    <div className="flex justify-between items-center">
      <span className="text-sm font-medium text-gray-800">{skill.name}</span>
      {showProficiency && skill.proficiency && <span className="text-xs text-gray-400">{skill.proficiency}%</span>}
    </div>
    {skill.proficiency && (
      <div className="w-full bg-gray-100 h-px">
        <div
          className={`h-px ${colors.primary.split(" ")[1].replace("text-", "bg-")}`}
          style={{ width: `${skill.proficiency}%` }}
        />
      </div>
    )}
  </div>
);

export default function SkillsDisplay({
  skills,
  colorScheme,
  variant = "progress-bars",
  groupByCategory = false,
  maxColumns = 2,
  showProficiency = true,
}: SkillsDisplayProps) {
  if (!skills || skills.length === 0) return null;

  const colors = getSkillsColorClasses(colorScheme);

  // Group skills by category if requested
  const skillGroups = groupByCategory ? groupSkillsByCategory(skills) : { "All Skills": skills };

  const renderSkill = (skill: Skill) => {
    const props = { skill, colors, showProficiency };

    switch (variant) {
      case "dots":
        return <DotsSkill key={skill.id} {...props} />;
      case "stars":
        return <StarsSkill key={skill.id} {...props} />;
      case "badges":
        return <BadgesSkill key={skill.id} {...props} />;
      case "simple":
        return <SimpleSkill key={skill.id} {...props} />;
      case "minimal":
        return <MinimalSkill key={skill.id} {...props} />;
      default:
        return <ProgressBarSkill key={skill.id} {...props} />;
    }
  };

  const getGridClasses = () => {
    if (variant === "badges") {
      return "flex flex-wrap gap-2";
    }
    return `grid grid-cols-1 ${maxColumns >= 2 ? "md:grid-cols-2" : ""} ${
      maxColumns >= 3 ? "lg:grid-cols-3" : ""
    } gap-4`;
  };

  return (
    <div className="space-y-6">
      {Object.entries(skillGroups).map(([category, categorySkills]) => (
        <div key={category}>
          {groupByCategory && Object.keys(skillGroups).length > 1 && (
            <h4 className={`text-base font-semibold mb-3 ${colors.primary.split(" ")[1]} uppercase tracking-wide`}>
              {category}
            </h4>
          )}
          <div className={getGridClasses()}>{categorySkills.map(renderSkill)}</div>
        </div>
      ))}
    </div>
  );
}
