import { But<PERSON> } from "@heroui/react";
import { Icon } from "@iconify/react";
import { PricingPlan } from "@/config/pricing";

interface PricingCardProps {
  plan: PricingPlan;
}

export const PricingCard = ({ plan }: PricingCardProps) => {
  return (
    <div
      className={`rounded-lg border p-6 ${plan.is_most_popular ? "border-purple-500" : "border-gray-200 dark:border-gray-700"}`}
    >
      <h3 className="text-lg font-semibold">{plan.name}</h3>
      <p className="mt-2 text-sm text-gray-500">{plan.description}</p>
      <p className="mt-4 text-4xl font-bold">{plan.price}</p>
      <ul className="mt-6 space-y-4">
        {plan.features.map((feature, index) => (
          <li key={index} className="flex items-center">
            <Icon icon="heroicons:check-circle-20-solid" className="mr-3 h-5 w-5 text-green-500" />
            <span>{feature}</span>
          </li>
        ))}
      </ul>
      <Button
        fullWidth
        className={`mt-6  ${plan.is_most_popular ? "bg-purple-500 hover:bg-purple-600" : "bg-gray-800 hover:bg-gray-900"}`}
      >
        {plan.id === "free" ? "Current Plan" : "Choose Plan"}
      </Button>
    </div>
  );
};
