import { Metadata } from "next";
import { notFound } from "next/navigation";
import { getPublicResumeById } from "@/actions/public-resume";
import { PublicResumeView } from "@/components/features/sharing/public-resume-view";
import { getClientIP, hashIP } from "@/lib/ip-utils";
import { ShareTokenService } from "@/lib/share-token";
import { generateResumeMetadata, generateResumeJsonLd } from "@/lib/metadata-utils";

interface PublicResumePageProps {
  params: Promise<{
    token: string;
  }>;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PublicResumePageProps): Promise<Metadata> {
  const { token } = await params;

  // Get client IP for rate limiting
  const clientIP = await getClientIP();
  const hashedIP = hashIP(clientIP);

  // Get resume ID from token with rate limiting
  const resumeId = await ShareTokenService.getResumeIdByToken(token, hashedIP);

  if (!resumeId) {
    return {
      title: "Resume Not Found",
      description: "The requested resume could not be found.",
    };
  }

  // Get resume data
  const result = await getPublicResumeById(resumeId);

  if (!result.success || !result.data) {
    return {
      title: "Resume Not Found",
      description: "The requested resume could not be found.",
    };
  }

  const resume = result.data;
  const url = `${process.env.NEXT_PUBLIC_APP_URL || "https://quickcv.com"}/share/${token}`;

  return generateResumeMetadata(resume, { url, titleSuffix: "QuickCV" });
}

export default async function PublicResumePage({ params }: PublicResumePageProps) {
  const { token } = await params;

  // Get client IP for rate limiting
  const clientIP = await getClientIP();
  const hashedIP = hashIP(clientIP);

  // Get resume ID from token with rate limiting (this also increments view count)
  const resumeId = await ShareTokenService.getResumeIdByToken(token, hashedIP);

  if (!resumeId) {
    notFound();
  }

  // Fetch the resume data
  const result = await getPublicResumeById(resumeId);

  if (!result.success || !result.data) {
    notFound();
  }

  const resume = result.data;

  // Add structured data for SEO
  const structuredData = generateResumeJsonLd(
    resume, 
    `${process.env.NEXT_PUBLIC_APP_URL || "https://quickcv.com"}/share/${token}`
  );

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      {/* Render the public resume view */}
      <PublicResumeView resume={resume} token={token} />
    </>
  );
}
