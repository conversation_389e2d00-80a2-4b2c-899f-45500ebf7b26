import { Metadata } from "next";
import { notFound } from "next/navigation";
import { getPublicWebsite } from "@/actions/websites";
import { getWebsiteTemplate } from "@/components/features/website/templates/template-registry";
import { FullResume } from "@/db/schema";
import { generateResumeMetadata, generateResumeJsonLd } from "@/lib/metadata-utils";

interface PublicWebsitePageProps {
  params: Promise<{
    slug: string;
  }>;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PublicWebsitePageProps): Promise<Metadata> {
  const { slug } = await params;
  const result = await getPublicWebsite(slug);

  if (!result.success || !result.data) {
    return {
      title: "CV Not Found",
      description: "The requested CV could not be found.",
    };
  }

  const { website } = result.data;
  const { resume } = website;
  const url = `${process.env.NEXT_PUBLIC_BASE_URL || "https://quickcv.com"}/cv/${slug}`;

  return generateResumeMetadata(resume, { url });
}

export default async function PublicWebsitePage({ params }: PublicWebsitePageProps) {
  const { slug } = await params;

  // Fetch the public website data
  const result = await getPublicWebsite(slug);

  if (!result.success || !result.data) {
    notFound();
  }

  const { website, resume } = result.data;
  const { websiteTemplate } = website;

  // Get the appropriate template component
  const TemplateComponent = getWebsiteTemplate(websiteTemplate?.slug || "");

  if (!TemplateComponent) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Template Not Available</h1>
          <p className="text-gray-600">The requested template is not available or has been removed.</p>
        </div>
      </div>
    );
  }

  // Add structured data for SEO
  const structuredData = generateResumeJsonLd(
    resume, 
    `${process.env.NEXT_PUBLIC_BASE_URL || "https://quickcv.com"}/cv/${slug}`
  );

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      {/* Render the website template */}
      <TemplateComponent resume={resume as FullResume} website={website} />
    </>
  );
}
