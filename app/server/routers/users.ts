import { eq } from "drizzle-orm";
import { db } from "@/db";
import { resumes, users } from "@/db/schema";
import { protectedProcedure, router } from "../trpc";

export const usersRouter = router({
  getPlan: protectedProcedure.query(async ({ ctx }) => {
    const [user] = await db
      .select({
        planId: users.planId,
      })
      .from(users)
      .where(eq(users.clerkId, ctx.user.clerkId))
      .limit(1);

    const resumeCount = await db.select().from(resumes).where(eq(resumes.userId, ctx.user.clerkId));

    return {
      isFree: user.planId === "free",
      resumeCount: resumeCount.length,
    };
  }),
});

export type UsersRouter = typeof usersRouter;
