import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { users } from "@/db/schema";
import { protectedProcedure, router } from "../trpc";

export const userProfileRouter = router({
  get: protectedProcedure.query(async ({ ctx }) => {
    const user = await db
      .select({
        id: users.clerkId,
        firstName: users.firstName,
        lastName: users.lastName,
        jobTitle: users.jobTitle,
        phone: users.phone,
        email: users.emailAddress,
        website: users.website,
        bio: users.bio,
        address: users.address,
        street: users.street,
        city: users.city,
        country: users.country,
      })
      .from(users)
      .where(eq(users.clerkId, ctx.user.clerkId))
      .limit(1);

    if (!user[0]) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "User profile not found",
      });
    }

    return user[0];
  }),

  update: protectedProcedure
    .input(
      z.object({
        firstName: z.string().optional(),
        lastName: z.string().optional(),
        jobTitle: z.string().optional(),
        phone: z.string().optional(),
        website: z.string().optional(),
        bio: z.string().optional(),
        address: z.string().optional(),
        street: z.string().optional(),
        city: z.string().optional(),
        country: z.string().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const result = await db
        .update(users)
        .set({
          ...input,
          updatedAt: new Date().toISOString(),
        })
        .where(eq(users.clerkId, ctx.user.clerkId))
        .returning();

      if (result.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      return {
        success: true,
        profile: {
          id: result[0].id,
          firstName: result[0].firstName,
          lastName: result[0].lastName,
          jobTitle: result[0].jobTitle,
          phone: result[0].phone,
          email: result[0].emailAddress,
          website: result[0].website,
          bio: result[0].bio,
          address: result[0].address,
          street: result[0].street,
          city: result[0].city,
          country: result[0].country,
        },
      };
    }),
});

export type UserProfileRouter = typeof userProfileRouter;
