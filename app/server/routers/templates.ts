import { asc, eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { templates } from "@/db/schema";
import { publicProcedure, router } from "../trpc";

export const templatesRouter = router({
  getTemplates: publicProcedure
    .input(
      z.object({
        isPremium: z.boolean().optional(),
      }),
    )
    .query(async ({ input }) => {
      return await db
        .select()
        .from(templates)
        .where(input.isPremium ? eq(templates.isPremium, input.isPremium) : undefined)
        .orderBy(asc(templates.id));
    }),
});

export type TemplatesRouter = typeof templatesRouter;
