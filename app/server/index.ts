import { resumesRouter } from "./routers/resumes";
import { sharingRouter } from "./routers/sharing";
import { templatesRouter } from "./routers/templates";
import { userRouter } from "./routers/user";
import { userProfileRouter } from "./routers/userProfile";
import { usersRouter } from "./routers/users";
import { websitesRouter } from "./routers/websites";
import { router } from "./trpc";

export const appRouter = router({
  users: usersRouter,
  resumes: resumesRouter,
  sharing: sharingRouter,
  templates: templatesRouter,
  user: userRouter,
  userProfile: userProfileRouter,
  websites: websitesRouter,
});

export type AppRouter = typeof appRouter;
