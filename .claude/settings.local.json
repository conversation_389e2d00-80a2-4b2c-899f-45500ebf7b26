{"permissions": {"allow": ["Bash(npm uninstall:*)", "Bash(npx prisma generate:*)", "Bash(npx prisma:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm install:*)", "WebFetch(domain:vercel.com)", "Bash(rm:*)", "WebFetch(domain:www.npmjs.com)", "<PERSON><PERSON>(docker stop:*)", "<PERSON><PERSON>(docker rm:*)", "<PERSON><PERSON>(docker run:*)", "<PERSON><PERSON>(curl:*)", "Bash(docker logs:*)", "Bash(bun run:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(ls:*)", "mcp__serena__list_dir", "mcp__serena__search_for_pattern", "mcp__serena__find_file"], "deny": []}}