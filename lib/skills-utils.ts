/**
 * Utility functions for handling skills and proficiency levels
 */

export type ProficiencyLevel = "Expert" | "Advanced" | "Intermediate" | "Beginner" | "Novice";

/**
 * Convert numeric proficiency (0-100) to text level
 */
export function getProficiencyText(proficiency: number | null): ProficiencyLevel | "" {
  if (!proficiency || proficiency < 0) return "";
  if (proficiency >= 90) return "Expert";
  if (proficiency >= 75) return "Advanced";
  if (proficiency >= 60) return "Intermediate";
  if (proficiency >= 40) return "Beginner";
  return "Novice";
}

/**
 * Convert text proficiency level to numeric value (for reverse conversion)
 */
export function getProficiencyNumeric(level: ProficiencyLevel): number {
  const levelMap = {
    Expert: 95,
    Advanced: 80,
    Intermediate: 65,
    Beginner: 50,
    Novice: 25,
  };
  return levelMap[level] || 0;
}

/**
 * Normalize proficiency to 0-100 range
 */
export function normalizeProficiency(proficiency: number | null): number {
  if (!proficiency) return 0;
  return Math.max(0, Math.min(100, proficiency));
}

/**
 * Get proficiency for visual indicators (dots, stars) out of 5
 */
export function getProficiencyOutOfFive(proficiency: number | null): number {
  if (!proficiency) return 0;
  return Math.round((proficiency / 100) * 5);
}

/**
 * Group skills by category
 */
export function groupSkillsByCategory<T extends { category?: string | null }>(
  skills: T[],
): { [category: string]: T[] } {
  return skills.reduce((acc: { [key: string]: T[] }, skill) => {
    const category = skill.category || "General";
    if (!acc[category]) acc[category] = [];
    acc[category].push(skill);
    return acc;
  }, {});
}

/**
 * Sort skills by proficiency (highest first) then by name
 */
export function sortSkillsByProficiency<T extends { name?: string | null; proficiency?: number | null }>(
  skills: T[],
): T[] {
  return [...skills].sort((a, b) => {
    // First sort by proficiency (descending)
    const proficiencyA = a.proficiency || 0;
    const proficiencyB = b.proficiency || 0;
    if (proficiencyA !== proficiencyB) {
      return proficiencyB - proficiencyA;
    }

    // Then sort by name (ascending)
    const nameA = a.name || "";
    const nameB = b.name || "";
    return nameA.localeCompare(nameB);
  });
}

/**
 * Sort skills by category, then by proficiency within each category
 */
export function sortSkillsByCategoryAndProficiency<
  T extends {
    name?: string | null;
    proficiency?: number | null;
    category?: string | null;
  },
>(skills: T[]): T[] {
  return [...skills].sort((a, b) => {
    // First sort by category
    const categoryA = a.category || "General";
    const categoryB = b.category || "General";
    if (categoryA !== categoryB) {
      return categoryA.localeCompare(categoryB);
    }

    // Then by proficiency within category
    const proficiencyA = a.proficiency || 0;
    const proficiencyB = b.proficiency || 0;
    if (proficiencyA !== proficiencyB) {
      return proficiencyB - proficiencyA;
    }

    // Finally by name
    const nameA = a.name || "";
    const nameB = b.name || "";
    return nameA.localeCompare(nameB);
  });
}

/**
 * Filter skills by minimum proficiency level
 */
export function filterSkillsByMinProficiency<T extends { proficiency?: number | null }>(
  skills: T[],
  minProficiency: number,
): T[] {
  return skills.filter((skill) => (skill.proficiency || 0) >= minProficiency);
}

/**
 * Get top N skills by proficiency
 */
export function getTopSkills<T extends { proficiency?: number | null; name?: string | null }>(
  skills: T[],
  count: number,
): T[] {
  return sortSkillsByProficiency(skills).slice(0, count);
}

/**
 * Get color classes for a given color scheme
 */
export function getSkillsColorClasses(colorScheme: string) {
  const colorMap = {
    blue: {
      primary: "bg-blue-600 text-blue-600 border-blue-600",
      secondary: "bg-blue-100 text-blue-800",
      light: "bg-blue-50",
    },
    green: {
      primary: "bg-green-600 text-green-600 border-green-600",
      secondary: "bg-green-100 text-green-800",
      light: "bg-green-50",
    },
    purple: {
      primary: "bg-purple-600 text-purple-600 border-purple-600",
      secondary: "bg-purple-100 text-purple-800",
      light: "bg-purple-50",
    },
    red: {
      primary: "bg-red-600 text-red-600 border-red-600",
      secondary: "bg-red-100 text-red-800",
      light: "bg-red-50",
    },
    orange: {
      primary: "bg-orange-600 text-orange-600 border-orange-600",
      secondary: "bg-orange-100 text-orange-800",
      light: "bg-orange-50",
    },
    gray: {
      primary: "bg-gray-600 text-gray-600 border-gray-600",
      secondary: "bg-gray-100 text-gray-800",
      light: "bg-gray-50",
    },
  };

  return colorMap[colorScheme as keyof typeof colorMap] || colorMap.blue;
}

/**
 * Validate skill data
 */
export function validateSkill(skill: {
  name?: string | null;
  proficiency?: number | null;
  category?: string | null;
}): boolean {
  // Must have a name
  if (!skill.name || skill.name.trim().length === 0) return false;

  // Proficiency must be valid if provided
  if (skill.proficiency !== null && skill.proficiency !== undefined) {
    if (skill.proficiency < 0 || skill.proficiency > 100) return false;
  }

  return true;
}

/**
 * Generate skill suggestions based on category
 */
export function getSkillSuggestions(category: string): string[] {
  const suggestionMap: { [key: string]: string[] } = {
    Programming: [
      "JavaScript",
      "TypeScript",
      "Python",
      "Java",
      "C#",
      "C++",
      "Go",
      "Rust",
      "React",
      "Vue.js",
      "Angular",
      "Node.js",
      "Express.js",
      "Django",
      "Flask",
      "SQL",
      "MongoDB",
      "PostgreSQL",
      "MySQL",
      "Redis",
      "GraphQL",
    ],
    Design: [
      "Adobe Photoshop",
      "Adobe Illustrator",
      "Figma",
      "Sketch",
      "Adobe XD",
      "UI/UX Design",
      "Wireframing",
      "Prototyping",
      "Design Systems",
      "User Research",
      "Visual Design",
      "Brand Design",
    ],
    Management: [
      "Project Management",
      "Team Leadership",
      "Strategic Planning",
      "Agile/Scrum",
      "Budget Management",
      "Risk Management",
      "Stakeholder Management",
      "Process Improvement",
      "Change Management",
      "Performance Management",
    ],
    Marketing: [
      "Digital Marketing",
      "Content Marketing",
      "SEO",
      "SEM",
      "Social Media",
      "Email Marketing",
      "Analytics",
      "Brand Management",
      "Campaign Management",
      "Market Research",
      "Customer Acquisition",
      "Growth Hacking",
    ],
    General: [
      "Communication",
      "Problem Solving",
      "Critical Thinking",
      "Leadership",
      "Teamwork",
      "Time Management",
      "Adaptability",
      "Creativity",
      "Attention to Detail",
      "Customer Service",
      "Public Speaking",
      "Writing",
    ],
  };

  return suggestionMap[category] || suggestionMap["General"];
}

/**
 * Calculate skill coverage score (percentage of skills with proficiency data)
 */
export function calculateSkillCoverage<T extends { proficiency?: number | null }>(skills: T[]): number {
  if (skills.length === 0) return 0;

  const skillsWithProficiency = skills.filter((skill) => skill.proficiency !== null && skill.proficiency !== undefined);

  return Math.round((skillsWithProficiency.length / skills.length) * 100);
}

/**
 * Get average proficiency across all skills
 */
export function getAverageProficiency<T extends { proficiency?: number | null }>(skills: T[]): number {
  const skillsWithProficiency = skills.filter((skill) => skill.proficiency !== null && skill.proficiency !== undefined);

  if (skillsWithProficiency.length === 0) return 0;

  const sum = skillsWithProficiency.reduce((acc, skill) => acc + (skill.proficiency || 0), 0);
  return Math.round(sum / skillsWithProficiency.length);
}
