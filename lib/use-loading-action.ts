import { useCallback } from "react";
import { LoadingState, useLoadingStore } from "./loading-store";

export function useLoadingAction<T extends any[], R>(
  action: (...args: T) => Promise<R>,
  loadingId?: string,
  options?: {
    type?: LoadingState["type"];
    message?: string;
    onSuccess?: (result: R) => void;
    onError?: (error: Error) => void;
  },
): [(...args: T) => Promise<R>, boolean] {
  const { startLoading, stopLoading, isLoading } = useLoadingStore();
  const id = loadingId || `action-${Date.now()}-${crypto.randomUUID().replace(/-/g, "").substring(0, 9)}`;
  const loading = isLoading(id);

  const wrappedAction = useCallback(
    async (...args: T): Promise<R> => {
      try {
        startLoading(id, options?.type || "mutation", options?.message);
        const result = await action(...args);
        options?.onSuccess?.(result);
        return result;
      } catch (error) {
        const err = error instanceof Error ? error : new Error("Unknown error");
        options?.onError?.(err);
        throw err;
      } finally {
        stopLoading(id);
      }
    },
    [action, id, options, startLoading, stopLoading],
  );

  return [wrappedAction, loading];
}

export function useTRPCLoading() {
  const { startLoading, stopLoading } = useLoadingStore();

  const wrapTRPCMutation = useCallback(
    <T, R>(
      mutation: {
        mutateAsync: (input: T) => Promise<R>;
        isPending?: boolean;
        isLoading?: boolean;
      },
      loadingId?: string,
      message?: string,
    ) => {
      const id = loadingId || `trpc-${Date.now()}-${crypto.randomUUID().replace(/-/g, "").substring(0, 9)}`;

      return {
        mutateAsync: async (input: T) => {
          startLoading(id, "mutation", message);
          try {
            const result = await mutation.mutateAsync(input);
            return result;
          } finally {
            stopLoading(id);
          }
        },
        isLoading: mutation.isPending || mutation.isLoading || false,
      };
    },
    [startLoading, stopLoading],
  );

  return { wrapTRPCMutation };
}
