"use client";

// Global ResizeObserver fix for handling null/undefined elements
if (typeof window !== "undefined") {
  const OriginalResizeObserver = window.ResizeObserver;
  
  class PatchedResizeObserver implements ResizeObserver {
    private originalObserver: ResizeObserver;
    
    constructor(callback: ResizeObserverCallback) {
      this.originalObserver = new OriginalResizeObserver(callback);
    }
    
    observe(target: Element, options?: ResizeObserverOptions): void {
      if (!target) {
        console.warn("ResizeObserver.observe called with null/undefined target");
        return;
      }
      
      if (!(target instanceof Element)) {
        console.warn("ResizeObserver.observe called with non-Element target:", target);
        return;
      }
      
      try {
        this.originalObserver.observe(target, options);
      } catch (error) {
        console.warn("ResizeObserver.observe error:", error);
      }
    }
    
    unobserve(target: Element): void {
      if (!target || !(target instanceof Element)) {
        return;
      }
      this.originalObserver.unobserve(target);
    }
    
    disconnect(): void {
      this.originalObserver.disconnect();
    }
  }
  
  // Override the global ResizeObserver
  (window as any).ResizeObserver = PatchedResizeObserver;
}

export {};