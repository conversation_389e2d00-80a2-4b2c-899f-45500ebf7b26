import { Metadata } from "next";
import { stripHtmlTags } from "./utils";

interface ResumeData {
  firstName?: string | null;
  lastName?: string | null;
  jobTitle?: string | null;
  bio?: string | null;
  photo?: string | null;
  email?: string | null;
  website?: string | null;
  city?: string | null;
  country?: string | null;
  experiences?: Array<{
    id: number;
    title?: string | null;
    company?: string | null;
    description?: string | null;
    startDate?: string;
    endDate?: string;
  }>;
  educations?: Array<{
    id: number;
    degree?: string | null;
    institution?: string | null;
    startDate?: string;
    endDate?: string;
  }>;
}

interface MetadataOptions {
  url: string;
  titleSuffix?: string;
  includeJsonLd?: boolean;
}

/**
 * Generate consistent metadata for resume pages
 */
export function generateResumeMetadata(
  resume: ResumeData,
  options: MetadataOptions
): Metadata {
  const fullName = `${resume.firstName || ""} ${resume.lastName || ""}`.trim();
  const title = resume.jobTitle || fullName;
  const description = resume.bio
    ? stripHtmlTags(resume.bio).substring(0, 160)
    : `Professional resume for ${fullName}`;

  const pageTitle = options.titleSuffix 
    ? `${title} | ${fullName} - ${options.titleSuffix}`
    : `${title} | ${fullName}`;

  return {
    title: pageTitle,
    description,
    openGraph: {
      title: `${title} | ${fullName}`,
      description,
      url: options.url,
      siteName: "QuickCV",
      type: "profile",
      images: resume.photo
        ? [
            {
              url: resume.photo,
              width: 400,
              height: 400,
              alt: fullName,
            },
          ]
        : [],
    },
    twitter: {
      card: "summary_large_image",
      title: `${title} | ${fullName}`,
      description,
      images: resume.photo ? [resume.photo] : [],
    },
    alternates: {
      canonical: options.url,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

/**
 * Generate JSON-LD structured data for resume
 */
export function generateResumeJsonLd(resume: ResumeData, url: string) {
  const fullName = `${resume.firstName || ""} ${resume.lastName || ""}`.trim();

  return {
    "@context": "https://schema.org",
    "@type": "Person",
    name: fullName,
    jobTitle: resume.jobTitle,
    url,
    ...(resume.email && { email: resume.email }),
    ...(resume.website && { url: resume.website }),
    ...(resume.photo && { image: resume.photo }),
    ...(resume.bio && {
      description: stripHtmlTags(resume.bio).substring(0, 200),
    }),
    ...((resume.city || resume.country) && {
      address: {
        "@type": "PostalAddress",
        ...(resume.city && { addressLocality: resume.city }),
        ...(resume.country && { addressCountry: resume.country }),
      },
    }),
    ...(resume.experiences &&
      resume.experiences.length > 0 && {
        workExperience: resume.experiences.map((exp) => ({
          "@type": "WorkExperience",
          name: exp.title,
          description: exp.description ? stripHtmlTags(exp.description) : undefined,
          employer: {
            "@type": "Organization",
            name: exp.company,
          },
          ...(exp.startDate && { startDate: exp.startDate }),
          ...(exp.endDate && { endDate: exp.endDate }),
        })),
      }),
    ...(resume.educations &&
      resume.educations.length > 0 && {
        education: resume.educations.map((edu) => ({
          "@type": "EducationalOccupationalCredential",
          name: edu.degree,
          educationalCredentialAwarded: edu.degree,
          provider: {
            "@type": "EducationalOrganization",
            name: edu.institution,
          },
          ...(edu.startDate && { startDate: edu.startDate }),
          ...(edu.endDate && { endDate: edu.endDate }),
        })),
      }),
  };
}