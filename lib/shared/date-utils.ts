// Removed translation dependencies - app is now English-only

// Date format types for different use cases
export type DateFormat = "short" | "long" | "numeric" | "year-only" | "full";
export type DateSeparator = "-" | "–" | "to" | "/";

// Default date configuration (English-only)
const DEFAULT_DATE_CONFIG = {
  separator: "–",
  rtl: false,
} as const;

// Validate and parse date string
const parseDate = (dateString: string): Date | null => {
  if (!dateString || dateString.trim() === "") return null;

  const date = new Date(dateString);
  return isNaN(date.getTime()) ? null : date;
};

// Get appropriate Intl.DateTimeFormat options based on format type
const getDateFormatOptions = (format: DateFormat): Intl.DateTimeFormatOptions => {
  switch (format) {
    case "short":
      return { year: "numeric", month: "short" };
    case "long":
      return { year: "numeric", month: "long" };
    case "numeric":
      return { year: "numeric", month: "2-digit" };
    case "year-only":
      return { year: "numeric" };
    case "full":
      return { year: "numeric", month: "long", day: "numeric" };
    default:
      return { year: "numeric", month: "short" };
  }
};

/**
 * Enhanced date formatting with ATS compatibility and semantic HTML support
 * @param dateString - ISO date string or Date object
 * @param format - Date format type
 * @param locale - Locale for formatting
 * @param options - Additional formatting options
 */
export const formatDate = (
  dateString: string | Date | null | undefined,
  format: DateFormat = "short",
  locale: string = "en-US", // Kept for backward compatibility but not used
  options?: {
    showPresent?: boolean;
    presentText?: string;
    yearOnly?: boolean;
  },
): string => {
  const { showPresent = false, presentText, yearOnly = false } = options || {};

  // Handle present/current cases
  if (!dateString && showPresent) {
    return presentText || "Present";
  }

  if (!dateString) return "";

  const date = typeof dateString === "string" ? parseDate(dateString) : dateString;
  if (!date) return "";

  // Force year-only if requested
  const actualFormat = yearOnly ? "year-only" : format;
  const formatOptions = getDateFormatOptions(actualFormat);

  try {
    // Use English locale for formatting
    return date.toLocaleDateString("en-US", formatOptions);
  } catch (error) {
    // Fallback to basic formatting
    console.warn("Date formatting failed:", error);
    return date.toLocaleDateString("en-US", formatOptions);
  }
};

/**
 * Legacy function for backward compatibility
 */
export const formatDateForLocale = (
  dateString: string,
  locale: string = "en-US",
  options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
  },
) => {
  if (!dateString) return "";

  const date = parseDate(dateString);
  if (!date) return "";

  try {
    return date.toLocaleDateString("en-US", options);
  } catch (error) {
    console.warn(`Legacy date formatting failed:`, error);
    return date.toLocaleDateString("en-US", options);
  }
};

/**
 * Format date range with enhanced ATS compatibility
 * @param startDate - Start date string
 * @param endDate - End date string
 * @param isCurrent - Whether the position/education is current (0 or 1)
 * @param locale - Locale for formatting
 * @param options - Additional formatting options
 */
export const formatDateRange = (
  startDate: string | null | undefined,
  endDate: string | null | undefined,
  isCurrent: number | boolean | null = 0,
  locale: string = "en-US",
  options?: {
    format?: DateFormat;
    separator?: DateSeparator;
    presentText?: string;
    ongoingText?: string;
    showDuration?: boolean;
  },
): string => {
  const { format = "short", separator, presentText, ongoingText, showDuration = false } = options || {};

  const dateSeparator = separator || DEFAULT_DATE_CONFIG.separator;
  const isCurrentPosition = Boolean(isCurrent || 0);

  // Format start date
  const formattedStart = formatDate(startDate, format, locale);

  // Handle current positions
  if (isCurrentPosition) {
    const currentText = presentText || ongoingText || "Present";
    return formattedStart ? `${formattedStart} ${dateSeparator} ${currentText}` : currentText;
  }

  // Format end date
  const formattedEnd = formatDate(endDate, format, locale);

  // Handle single dates
  if (formattedStart && !formattedEnd) {
    return formattedStart;
  }

  if (!formattedStart && formattedEnd) {
    return formattedEnd;
  }

  // Handle complete ranges
  if (formattedStart && formattedEnd) {
    let result = `${formattedStart} ${dateSeparator} ${formattedEnd}`;

    // Add duration if requested
    if (showDuration) {
      const duration = calculateDuration(startDate, endDate, locale);
      if (duration) {
        result += ` (${duration})`;
      }
    }

    return result;
  }

  return "";
};

/**
 * Calculate duration between two dates
 * @param startDate - Start date string
 * @param endDate - End date string
 * @param locale - Locale for text formatting
 */
export const calculateDuration = (
  startDate: string | null | undefined,
  endDate: string | null | undefined,
  locale: string = "en-US",
): string => {
  const start = parseDate(startDate || "");
  const end = parseDate(endDate || "") || new Date();

  if (!start || !end) return "";

  const diffMs = end.getTime() - start.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  const diffMonths = Math.floor(diffDays / 30.44); // Average days per month
  const years = Math.floor(diffMonths / 12);
  const months = diffMonths % 12;

  return formatDuration(years, months);
};

/**
 * Format duration string (English-only)
 * @param years - Number of years
 * @param months - Number of months
 */
const formatDuration = (years: number, months: number): string => {
  if (years === 0 && months === 0) {
    return "Less than a month";
  }

  if (years === 0) {
    return months === 1 ? "1 month" : `${months} months`;
  }

  if (months === 0) {
    return years === 1 ? "1 year" : `${years} years`;
  }

  // Both years and months present
  if (years === 1 && months === 1) {
    return "1 year 1 month";
  } else if (years === 1) {
    return `1 year ${months} months`;
  } else if (months === 1) {
    return `${years} years 1 month`;
  } else {
    return `${years} years ${months} months`;
  }
};

/**
 * Create semantic HTML time element with datetime attribute for ATS compatibility
 * @param dateString - Date string to format
 * @param displayText - Text to display (optional, will format date if not provided)
 * @param format - Date format for display
 * @param locale - Locale for formatting
 */
export const createSemanticTimeElement = (
  dateString: string | null | undefined,
  displayText?: string,
  format: DateFormat = "short",
  locale: string = "en-US",
): { dateTime: string; display: string } => {
  if (!dateString) {
    return { dateTime: "", display: displayText || "" };
  }

  const date = parseDate(dateString);
  if (!date) {
    return { dateTime: "", display: displayText || "" };
  }

  // ISO 8601 format for datetime attribute (ATS friendly)
  const dateTime = date.toISOString().split("T")[0];

  // Human readable display text
  const display = displayText || formatDate(dateString, format, locale);

  return { dateTime, display };
};

/**
 * Format date range with semantic HTML time elements
 * @param startDate - Start date string
 * @param endDate - End date string
 * @param isCurrent - Whether current position
 * @param locale - Locale for formatting
 * @param options - Additional options
 */
export const createSemanticDateRange = (
  startDate: string | null | undefined,
  endDate: string | null | undefined,
  isCurrent: number | boolean | null = 0,
  locale: string = "en-US", // Kept for backward compatibility but not used
  options?: {
    format?: DateFormat;
    separator?: DateSeparator;
    presentText?: string;
  },
): {
  startDateTime: string;
  endDateTime: string;
  displayText: string;
  isRange: boolean;
} => {
  const { format = "short", separator, presentText } = options || {};
  const dateSeparator = separator || DEFAULT_DATE_CONFIG.separator;
  const isCurrentPosition = Boolean(isCurrent || 0);

  const startTime = createSemanticTimeElement(startDate, undefined, format, locale);

  if (isCurrentPosition) {
    const currentTextFinal = presentText || "Present";
    return {
      startDateTime: startTime.dateTime,
      endDateTime: "",
      displayText: startTime.display ? `${startTime.display} ${dateSeparator} ${currentTextFinal}` : currentTextFinal,
      isRange: Boolean(startTime.display),
    };
  }

  const endTime = createSemanticTimeElement(endDate, undefined, format, locale);

  if (startTime.display && endTime.display) {
    return {
      startDateTime: startTime.dateTime,
      endDateTime: endTime.dateTime,
      displayText: `${startTime.display} ${dateSeparator} ${endTime.display}`,
      isRange: true,
    };
  }

  if (startTime.display) {
    return {
      startDateTime: startTime.dateTime,
      endDateTime: "",
      displayText: startTime.display,
      isRange: false,
    };
  }

  if (endTime.display) {
    return {
      startDateTime: "",
      endDateTime: endTime.dateTime,
      displayText: endTime.display,
      isRange: false,
    };
  }

  return {
    startDateTime: "",
    endDateTime: "",
    displayText: "",
    isRange: false,
  };
};
