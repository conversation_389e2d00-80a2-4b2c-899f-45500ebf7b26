/**
 * Template utility functions for resume templates
 */

// Define the color scheme type
export type ColorScheme = 'blue' | 'green' | 'purple' | 'red' | 'orange' | 'teal' | 'pink' | 'indigo';

// Standard color classes used across templates
export const TEMPLATE_COLOR_CLASSES = {
  blue: "text-blue-700 bg-blue-700 border-blue-700",
  green: "text-green-700 bg-green-700 border-green-700",
  purple: "text-purple-700 bg-purple-700 border-purple-700",
  red: "text-red-700 bg-red-700 border-red-700",
  orange: "text-orange-700 bg-orange-700 border-orange-700",
  teal: "text-teal-700 bg-teal-700 border-teal-700",
  pink: "text-pink-700 bg-pink-700 border-pink-700",
  indigo: "text-indigo-700 bg-indigo-700 border-indigo-700",
} as const;

/**
 * Get the color classes for a resume's color scheme
 * @param colorScheme - The color scheme from the resume
 * @returns The CSS classes for the selected color scheme
 */
export function getTemplateColorClasses(colorScheme?: string | null): string {
  if (!colorScheme || !(colorScheme in TEMPLATE_COLOR_CLASSES)) {
    return TEMPLATE_COLOR_CLASSES.blue;
  }
  
  return TEMPLATE_COLOR_CLASSES[colorScheme as ColorScheme];
}

/**
 * Extract specific color class from the combined classes
 * @param colorClasses - The combined color classes string
 * @param type - The type of class to extract ('text', 'bg', 'border')
 * @returns The specific color class
 */
export function extractColorClass(colorClasses: string, type: 'text' | 'bg' | 'border'): string {
  const classes = colorClasses.split(' ');
  return classes.find(cls => cls.startsWith(type)) || '';
}

/**
 * Get template font family class
 * @param fontFamily - The font family from the resume
 * @returns The font family class
 */
export function getTemplateFontClass(fontFamily?: string | null): string {
  return `font-${fontFamily || "inter"}`;
}