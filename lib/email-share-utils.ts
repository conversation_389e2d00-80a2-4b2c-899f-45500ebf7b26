/**
 * Shared utilities for email sharing functionality
 */

import { toast } from "react-hot-toast";

// Common state interface for email variables
export interface EmailVariables extends Record<string, string> {
  recipientName: string;
  position: string;
  companyName: string;
  personalPitch: string;
  mutualConnection: string;
  industry: string;
  meetingContext: string;
  followUpNote: string;
  personalNote: string;
  yearsOfExperience: string;
}

// Generated email interface
export interface GeneratedEmail {
  subject: string;
  body: string;
  resumeUrl: string;
}

// Common modal props interface
export interface EmailShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  resumeId: number;
}

// Initial empty state for email variables
export const createInitialEmailVariables = (): EmailVariables => ({
  recipientName: "",
  position: "",
  companyName: "",
  personalPitch: "",
  mutualConnection: "",
  industry: "",
  meetingContext: "",
  followUpNote: "",
  personalNote: "",
  yearsOfExperience: "",
});

// Common email handling functions
export const handleSendEmail = (generatedEmail: GeneratedEmail | null, editableEmail?: { subject: string; body: string } | null) => {
  const emailToSend = editableEmail || generatedEmail;
  if (!emailToSend) return;

  const mailtoLink = `mailto:?subject=${encodeURIComponent(emailToSend.subject)}&body=${encodeURIComponent(emailToSend.body)}`;

  // Try to detect if mailto will work
  const isEmailClientAvailable = /(iPad|iPhone|iPod|Mac|Windows|Linux)/i.test(navigator.userAgent);

  if (isEmailClientAvailable) {
    // Use location.href for better compatibility
    window.location.href = mailtoLink;
    toast.success("Opening your email client...");
  } else {
    // Fallback: copy the email and show instructions
    handleCopyEmail(generatedEmail, editableEmail);
    toast.success("Email copied to clipboard!");
  }
};

export const handleCopyEmail = async (generatedEmail: GeneratedEmail | null, editableEmail?: { subject: string; body: string } | null) => {
  const emailToSend = editableEmail || generatedEmail;
  if (!emailToSend) return;

  const emailContent = `Subject: ${emailToSend.subject}\n\n${emailToSend.body}`;

  try {
    await navigator.clipboard.writeText(emailContent);
    toast.success("Email copied to clipboard!");
  } catch (error) {
    toast.error("Failed to copy email. Please try again.");
  }
};

// Modal configuration constants
export const EMAIL_MODAL_CONFIG = {
  size: "4xl" as const,
  scrollBehavior: "inside" as const,
  classNames: {
    base: "max-h-[90vh]",
    body: "py-6",
  },
};