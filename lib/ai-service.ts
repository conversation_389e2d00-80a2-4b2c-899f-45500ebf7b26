interface ResumeContext {
  firstName?: string;
  lastName?: string;
  jobTitle?: string;
  bio?: string;
  skills?: Array<{ name: string; category?: string }>;
}

interface ItemContext {
  title?: string;
  company?: string;
  organization?: string;
  institution?: string;
  startDate?: string;
  endDate?: string;
  isCurrent?: boolean;
  role?: string;
  degree?: string;
  fieldOfStudy?: string;
  issuer?: string;
  currentDescription?: string;
}

export interface AIOptions {
  tone: "professional" | "casual" | "technical";
  length: "short" | "medium" | "detailed";
  language: "en" | "ar";
}

export type ContextType =
  | "experience"
  | "project"
  | "education"
  | "award"
  | "certification"
  | "hobby"
  | "volunteering"
  | "reference"
  | "bio";

interface GenerateDescriptionResponse {
  success: boolean;
  description?: string;
  error?: string;
  contextType?: ContextType;
  prompt?: string; // Only in development
}

export class AIService {
  private static instance: AIService;

  private constructor() {}

  static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  async generateDescription(
    contextType: ContextType,
    resumeContext: ResumeContext,
    itemContext: ItemContext = {},
    options: AIOptions = {
      tone: "professional",
      length: "medium",
      language: "en",
    },
  ): Promise<GenerateDescriptionResponse> {
    try {
      const response = await fetch("/api/ai/generate-description", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contextType,
          resumeContext,
          itemContext,
          options,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data: GenerateDescriptionResponse = await response.json();
      return data;
    } catch (error) {
      console.error("AI service error:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Extract context from resume data for AI generation
   */
  extractResumeContext(resumeData: any): ResumeContext {
    return {
      firstName: resumeData?.firstName,
      lastName: resumeData?.lastName,
      jobTitle: resumeData?.jobTitle,
      bio: resumeData?.bio,
      skills: resumeData?.skills || [],
    };
  }

  /**
   * Get context type from field name and form schema
   */
  getContextTypeFromField(fieldName: string, schemaEntity?: string): ContextType {
    // Map schema entities to context types
    const entityMap: Record<string, ContextType> = {
      experience: "experience",
      project: "project",
      education: "education",
      award: "award",
      certification: "certification",
      hobby: "hobby",
      volunteering: "volunteering",
      reference: "reference",
    };

    if (schemaEntity && entityMap[schemaEntity]) {
      return entityMap[schemaEntity];
    }

    // Fallback based on field name
    if (fieldName === "bio") {
      return "bio";
    }

    // Default to experience for description fields
    return "experience";
  }

  /**
   * Extract item context from current form item
   */
  extractItemContext(item: any, contextType: ContextType): ItemContext {
    const context: ItemContext = {
      currentDescription: item?.description,
    };

    switch (contextType) {
      case "experience":
        return {
          ...context,
          title: item?.title,
          company: item?.company,
          startDate: item?.startDate,
          endDate: item?.endDate,
          isCurrent: item?.isCurrent === 1 || item?.isCurrent === true,
        };

      case "project":
        return {
          ...context,
          title: item?.title,
          company: item?.client, // Projects use 'client' field
          startDate: item?.startDate,
          endDate: item?.endDate,
        };

      case "education":
        return {
          ...context,
          degree: item?.degree,
          institution: item?.institution,
          fieldOfStudy: item?.fieldOfStudy,
          startDate: item?.startDate,
          endDate: item?.endDate,
        };

      case "award":
      case "certification":
        return {
          ...context,
          title: item?.title,
          issuer: item?.issuer,
        };

      case "volunteering":
        return {
          ...context,
          role: item?.role,
          organization: item?.organization,
          startDate: item?.startDate,
          endDate: item?.endDate,
        };

      case "reference":
        return {
          ...context,
          title: item?.name,
          company: item?.company,
          role: item?.position,
        };

      case "hobby":
        return {
          ...context,
          title: item?.name,
        };

      default:
        return context;
    }
  }

  /**
   * Validate if AI generation is available for the current field
   */
  isAIAvailableForField(fieldName: string): boolean {
    const supportedFields = ["description", "bio"];
    return supportedFields.includes(fieldName);
  }

  /**
   * Get default AI options based on context and user preferences
   */
  getDefaultOptions(contextType: ContextType, userLanguage: "en" | "ar" = "en"): AIOptions {
    const lengthByContext: Record<ContextType, "short" | "medium" | "detailed"> = {
      bio: "medium",
      experience: "medium",
      project: "medium",
      education: "short",
      award: "short",
      certification: "short",
      hobby: "short",
      volunteering: "medium",
      reference: "short",
    };

    return {
      tone: "professional",
      length: lengthByContext[contextType] || "medium",
      language: userLanguage,
    };
  }
}

// Export singleton instance
export const aiService = AIService.getInstance();
