import { type ClassValue, clsx } from "clsx";

export function cn(...inputs: ClassValue[]) {
  return clsx(inputs);
}

/**
 * Get initials from a name
 */
export function getInitials(name: string): string {
  if (!name) return "";

  const words = name.trim().split(/\s+/);
  if (words.length === 1) {
    return words[0].charAt(0).toUpperCase();
  }

  return words
    .slice(0, 2)
    .map((word) => word.charAt(0).toUpperCase())
    .join("");
}

/**
 * Uppercase the first letter of a string
 */
export function upperCaseFirstLetter(str: string): string {
  if (!str) return "";
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * Safely strips HTML tags from a string without ReDoS vulnerability
 * Uses DOMParser for secure HTML parsing instead of vulnerable regex
 * Fallback to simple regex for server-side environments
 */
export function stripHtmlTags(html: string): string {
  if (!html || typeof html !== 'string') {
    return '';
  }

  // Truncate extremely long strings to prevent DoS
  const maxLength = 50000; // 50KB max
  const safeHtml = html.length > maxLength ? html.substring(0, maxLength) : html;

  // Browser environment - use DOMParser for safe HTML parsing
  if (typeof window !== 'undefined' && window.DOMParser) {
    try {
      const doc = new DOMParser().parseFromString(safeHtml, 'text/html');
      return doc.body.textContent || doc.body.innerText || '';
    } catch (error) {
      console.warn('DOMParser failed, falling back to regex:', error);
    }
  }

  // Server-side environment - use optimized, safer regex approach
  // This approach uses possessive quantifiers to prevent backtracking
  return safeHtml
    .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '') // Remove scripts first
    .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '') // Remove styles
    .replace(/<[^>]+>/g, '') // Simple tag removal (safer than nested quantifiers)
    .replace(/&[#\w]+;/g, ' ') // Replace HTML entities with space
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
}

/**
 * Validates email format using a ReDoS-safe regex pattern
 * This regex is RFC 5322 compliant and prevents exponential backtracking
 * @param email - The email address to validate
 * @returns true if the email format is valid, false otherwise
 */
export function isValidEmail(email: string): boolean {
  if (!email || typeof email !== 'string') {
    return false;
  }

  // ReDoS-safe email regex that follows RFC 5322 specification
  // Uses atomic quantifiers and explicit character ranges to prevent backtracking
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  
  // Additional length check to prevent extremely long email addresses
  if (email.length > 254) {
    return false;
  }

  return emailRegex.test(email);
}
