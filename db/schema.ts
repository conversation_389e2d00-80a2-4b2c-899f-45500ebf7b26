import { createId } from "@paralleldrive/cuid2";
import { relations, sql } from "drizzle-orm";
import { index, integer, sqliteTable, text } from "drizzle-orm/sqlite-core";

// Users table
export const users = sqliteTable("users", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => createId()),
  clerkId: text("clerk_id").notNull().unique(),
  emailAddress: text("email_address").notNull().unique(),

  // Subscription fields
  planId: text("plan_id").default("free").notNull(), // e.g., "free", "pro_monthly", "pro_yearly"
  subscriptionId: text("subscription_id"),
  currentPeriodEnd: text("current_period_end"),

  paymentId: text("payment_id"),
  paymentGateway: text("payment_gateway"),
  paddleCustomerId: text("paddle_customer_id"),
  hasUsedFreeAI: integer("has_used_free_ai", { mode: "boolean" })
    .default(false)
    .notNull(),
  pdfExportCount: integer("pdf_export_count").default(0).notNull(),

  // Profile fields
  firstName: text().notNull().default(""),
  lastName: text().notNull().default(""),
  jobTitle: text().notNull().default(""),
  phone: text("phone").notNull().default(""),
  website: text("website").notNull().default(""),
  bio: text("bio").notNull().default(""),
  address: text("address").notNull().default(""),
  street: text("street").notNull().default(""),
  city: text("city").notNull().default(""),
  country: text("country").notNull().default(""),
  createdAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
});

// Templates table
export const templates = sqliteTable("templates", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  slug: text("slug").notNull().unique(),
  isPremium: integer("is_premium", { mode: "boolean" })
    .default(false)
    .notNull(),
  atsScore: text("ats_score"),
  category: text("category"),
  features: text("features"),
  description: text("description"),
  image: text("image"),
  createdAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
});

// Resumes table
export const resumes = sqliteTable("resumes", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  title: text("title"),
  firstName: text("first_name").notNull().default(""),
  lastName: text("last_name").notNull().default(""),
  jobTitle: text("job_title").notNull().default(""),
  address: text("address").notNull().default(""),
  phone: text("phone").notNull().default(""),
  email: text("email").notNull().default(""),
  website: text("website").notNull().default(""),
  bio: text("bio").notNull().default(""),
  birthDate: text().notNull().default(""),
  city: text("city").notNull().default(""),
  street: text("street").notNull().default(""),
  country: text("country").notNull().default(""),
  showPhoto: integer("showPhoto").notNull().default(0),
  colorScheme: text("color_scheme").default("blue").notNull(),
  fontFamily: text("font_family").default("inter").notNull(),
  spacing: text("spacing").default("normal").notNull(),
  margins: text("margins").default("normal").notNull(),
  photo: text("photo").notNull().default(""),
  thumbnail: text("thumbnail").notNull().default(""),
  createdAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  userId: text("user_id").notNull(),
  templateId: integer("template_id").default(1).notNull(),
});

// Education table
export const educations = sqliteTable("educations", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  city: text("city"),
  country: text("country"),
  institution: text("institution"),
  isCurrent: integer("is_current").default(0),
  startDate: text("startDate").notNull().default(""),
  endDate: text("endDate").notNull().default(""),
  description: text("description"),
  degree: text("degree"),
  fieldOfStudy: text("field_of_study"),
  website: text("website"),
  sort: integer("sort").default(0),
  createdAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  resumeId: integer("resume_id").notNull(),
});

// Experience table
export const experiences = sqliteTable("experiences", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  title: text("title"),
  company: text("company"),
  startDate: text().notNull().default(""),
  endDate: text().notNull().default(""),
  description: text("description"),
  city: text("city"),
  country: text("country"),
  isCurrent: integer("is_current").default(0).notNull(),
  sort: integer("sort").default(0),
  createdAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  resumeId: integer("resume_id").notNull(),
});

// Projects table
export const projects = sqliteTable("projects", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  title: text("title"),
  client: text("client"),
  url: text("url"),
  startDate: text().notNull().default(""),
  endDate: text().notNull().default(""),
  description: text("description"),
  sort: integer("sort").default(0),
  createdAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  resumeId: integer("resume_id").notNull(),
});

// Awards table
export const awards = sqliteTable("awards", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  title: text("title"),
  issuer: text("issuer"),
  url: text("url"),
  dateReceived: text("date_received"),
  description: text("description"),
  sort: integer("sort").default(0),
  createdAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  resumeId: integer("resume_id").notNull(),
});

// Certifications table
export const certifications = sqliteTable("certifications", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  title: text("title"),
  issuer: text("issuer"),
  url: text("url"),
  dateReceived: text("date_received"),
  description: text("description"),
  sort: integer("sort").default(0),
  createdAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  resumeId: integer("resume_id").notNull(),
});

// Skills table
export const skills = sqliteTable("skills", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  name: text("name"),
  proficiency: integer("proficiency"),
  category: text("category"),
  sort: integer("sort").default(0),
  createdAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  resumeId: integer("resume_id").notNull(),
});

// Languages table
export const languages = sqliteTable("languages", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  name: text("name"),
  proficiency: integer("proficiency").notNull().default(10),
  sort: integer("sort").default(0),
  createdAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  resumeId: integer("resume_id").notNull(),
});

// References table
export const references = sqliteTable("references", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  name: text("name"),
  company: text("company"),
  position: text("position"),
  email: text("email"),
  phone: text("phone"),
  description: text("description"),
  sort: integer("sort").default(0),
  createdAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  resumeId: integer("resume_id").notNull(),
});

// Hobbies table
export const hobbies = sqliteTable("hobbies", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  name: text("name"),
  description: text("description"),
  sort: integer("sort").default(0),
  createdAt: text("")
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text("")
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  resumeId: integer("resume_id").notNull(),
});

// Volunteering table
export const volunteerings = sqliteTable("volunteerings", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  organization: text("organization"),
  role: text("role"),
  startDate: text(),
  endDate: text(),
  description: text("description"),
  sort: integer("sort").default(0),
  createdAt: text("")
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text("")
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  resumeId: integer("resume_id").notNull(),
});

// Profiles table
export const profiles = sqliteTable("profiles", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  url: text("url"),
  username: text("username"),
  network: text("network").notNull().default(""),
  icon: text("icon"),
  sort: integer("sort").default(0),
  createdAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  resumeId: integer("resume_id").notNull(),
});

// Website Templates table
export const websiteTemplates = sqliteTable("website_templates", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  slug: text("slug").notNull().unique(),
  createdAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
});

// Websites table
export const websites = sqliteTable("websites", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  slug: text("slug").notNull().unique(),
  isPublic: integer("ispublic").default(0).notNull(),
  analytics: integer("analytics").default(0).notNull(),
  backgroundPattern: text("background_pattern").default("none").notNull(),
  createdAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  userId: text("user_id").notNull(),
  resumeId: integer("resume_id").notNull(),
  websiteTemplateId: integer("website_template_id").notNull(),
});

// Share tokens table for secure resume sharing
export const shareTokens = sqliteTable("share_tokens", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  token: text("token").notNull().unique(),
  resumeId: integer("resume_id").notNull(),
  expiresAt: text("expires_at").notNull(),
  viewCount: integer("view_count").default(0).notNull(),
  createdAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  resumes: many(resumes),
  websites: many(websites),
}));

export const templatesRelations = relations(templates, ({ many }) => ({
  resumes: many(resumes),
}));

export const resumesRelations = relations(resumes, ({ one, many }) => ({
  // One-to-one relations
  user: one(users, {
    fields: [resumes.userId],
    references: [users.clerkId],
  }),
  template: one(templates, {
    fields: [resumes.templateId],
    references: [templates.id],
  }),
  // One-to-many relations
  educations: many(educations),
  experiences: many(experiences),
  projects: many(projects),
  awards: many(awards),
  certifications: many(certifications),
  skills: many(skills),
  languages: many(languages),
  references: many(references),
  hobbies: many(hobbies),
  volunteerings: many(volunteerings),
  profiles: many(profiles),
  websites: many(websites),
  shareTokens: many(shareTokens),
}));

export const educationsRelations = relations(educations, ({ one }) => ({
  resume: one(resumes, {
    fields: [educations.resumeId],
    references: [resumes.id],
  }),
}));

export const experiencesRelations = relations(experiences, ({ one }) => ({
  resume: one(resumes, {
    fields: [experiences.resumeId],
    references: [resumes.id],
  }),
}));

export const projectsRelations = relations(projects, ({ one }) => ({
  resume: one(resumes, {
    fields: [projects.resumeId],
    references: [resumes.id],
  }),
}));

export const awardsRelations = relations(awards, ({ one }) => ({
  resume: one(resumes, {
    fields: [awards.resumeId],
    references: [resumes.id],
  }),
}));

export const certificationsRelations = relations(certifications, ({ one }) => ({
  resume: one(resumes, {
    fields: [certifications.resumeId],
    references: [resumes.id],
  }),
}));

export const skillsRelations = relations(skills, ({ one }) => ({
  resume: one(resumes, {
    fields: [skills.resumeId],
    references: [resumes.id],
  }),
}));

export const languagesRelations = relations(languages, ({ one }) => ({
  resume: one(resumes, {
    fields: [languages.resumeId],
    references: [resumes.id],
  }),
}));

export const referencesRelations = relations(references, ({ one }) => ({
  resume: one(resumes, {
    fields: [references.resumeId],
    references: [resumes.id],
  }),
}));

export const hobbiesRelations = relations(hobbies, ({ one }) => ({
  resume: one(resumes, {
    fields: [hobbies.resumeId],
    references: [resumes.id],
  }),
}));

export const volunteeringsRelations = relations(volunteerings, ({ one }) => ({
  resume: one(resumes, {
    fields: [volunteerings.resumeId],
    references: [resumes.id],
  }),
}));

export const profilesRelations = relations(profiles, ({ one }) => ({
  resume: one(resumes, {
    fields: [profiles.resumeId],
    references: [resumes.id],
  }),
}));

export const websiteTemplatesRelations = relations(
  websiteTemplates,
  ({ many }) => ({
    websites: many(websites),
  }),
);

export const websitesRelations = relations(websites, ({ one }) => ({
  user: one(users, {
    fields: [websites.userId],
    references: [users.clerkId],
  }),
  resume: one(resumes, {
    fields: [websites.resumeId],
    references: [resumes.id],
  }),
  template: one(websiteTemplates, {
    fields: [websites.websiteTemplateId],
    references: [websiteTemplates.id],
  }),
}));

export const shareTokensRelations = relations(shareTokens, ({ one }) => ({
  resume: one(resumes, {
    fields: [shareTokens.resumeId],
    references: [resumes.id],
  }),
}));

// Support tickets table
export const supportTickets = sqliteTable("support_tickets", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  ticketNumber: text("ticket_number").notNull().unique(),
  name: text("name").notNull(),
  email: text("email").notNull(),
  subject: text("subject").notNull(),
  message: text("message").notNull(),
  category: text("category").notNull().default("general"), // general, technical, billing
  priority: text("priority").notNull().default("normal"), // low, normal, high, urgent
  status: text("status").notNull().default("open"), // open, in_progress, resolved, closed
  userId: text("user_id"), // Optional - for authenticated users
  assignedTo: text("assigned_to"), // Admin user handling the ticket
  resolutionMessage: text("resolution_message"),
  createdAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  resolvedAt: text("resolved_at"),
});

// Contact form submissions table
export const contactSubmissions = sqliteTable("contact_submissions", {
  id: integer("id", { mode: "number" }).primaryKey({ autoIncrement: true }),
  name: text("name").notNull(),
  email: text("email").notNull(),
  subject: text("subject").notNull(),
  message: text("message").notNull(),
  category: text("category").notNull().default("general"), // general, technical, billing, feature, account
  priority: text("priority").notNull().default("normal"), // low, normal, high, urgent
  status: text("status").notNull().default("new"), // new, read, replied
  attachments: text("attachments"), // JSON array of file paths
  userId: text("user_id"), // Optional - for authenticated users
  assignedTo: text("assigned_to"), // Admin user handling the submission
  responseMessage: text("response_message"), // Admin response
  createdAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: text()
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  respondedAt: text("responded_at"), // When admin responded
});

// Relations for support tickets
export const supportTicketsRelations = relations(supportTickets, ({ one }) => ({
  user: one(users, {
    fields: [supportTickets.userId],
    references: [users.id],
  }),
}));

// Relations for contact submissions
export const contactSubmissionsRelations = relations(
  contactSubmissions,
  ({ one }) => ({
    user: one(users, {
      fields: [contactSubmissions.userId],
      references: [users.id],
    }),
  }),
);

// Export all table types for use in application code
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

export type Template = typeof templates.$inferSelect;
export type NewTemplate = typeof templates.$inferInsert;

export type Resume = typeof resumes.$inferSelect;
export type NewResume = typeof resumes.$inferInsert;

export type Education = typeof educations.$inferSelect;
export type NewEducation = typeof educations.$inferInsert;

export type Experience = typeof experiences.$inferSelect;
export type NewExperience = typeof experiences.$inferInsert;

export type Project = typeof projects.$inferSelect;
export type NewProject = typeof projects.$inferInsert;

export type Award = typeof awards.$inferSelect;
export type NewAward = typeof awards.$inferInsert;

export type Certification = typeof certifications.$inferSelect;
export type NewCertification = typeof certifications.$inferInsert;

export type Skill = typeof skills.$inferSelect;
export type NewSkill = typeof skills.$inferInsert;

export type Language = typeof languages.$inferSelect;
export type NewLanguage = typeof languages.$inferInsert;

export type Reference = typeof references.$inferSelect;
export type NewReference = typeof references.$inferInsert;

export type Hobby = typeof hobbies.$inferSelect;
export type NewHobby = typeof hobbies.$inferInsert;

export type Volunteering = typeof volunteerings.$inferSelect;
export type NewVolunteering = typeof volunteerings.$inferInsert;

export type Profile = typeof profiles.$inferSelect;
export type NewProfile = typeof profiles.$inferInsert;

export type WebsiteTemplate = typeof websiteTemplates.$inferSelect;
export type NewWebsiteTemplate = typeof websiteTemplates.$inferInsert;

export type Website = typeof websites.$inferSelect;
export type NewWebsite = typeof websites.$inferInsert;

export type ShareToken = typeof shareTokens.$inferSelect;
export type NewShareToken = typeof shareTokens.$inferInsert;

export type SupportTicket = typeof supportTickets.$inferSelect;
export type NewSupportTicket = typeof supportTickets.$inferInsert;

export type ContactSubmission = typeof contactSubmissions.$inferSelect;
export type NewContactSubmission = typeof contactSubmissions.$inferInsert;

// Feature flag types removed - A/B testing functionality removed

// Feature flag relations removed - A/B testing functionality removed

// Website with all relations for frontend usage
export interface FullWebsite extends Website {
  resume: Resume | null;
  websiteTemplate: WebsiteTemplate | null;
}

// Resume with all nested collections - matching the frontend expectations
export interface FullResume extends Resume {
  template?: Template | null;
  template_name?: string;
  colorScheme: string;
  fontFamily: string;
  // All nested collections
  educations: Education[];
  experiences: Experience[];
  projects: Project[];
  awards: Award[];
  certifications: Certification[];
  skills: Skill[];
  languages: Language[];
  references: Reference[];
  hobbies: Hobby[];
  volunteerings: Volunteering[];
  profiles: Profile[];
}
